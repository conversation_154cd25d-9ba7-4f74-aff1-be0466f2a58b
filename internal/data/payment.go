package data

import (
	"context"
	"encoding/json"
	"errors"
	"strings"
	"time"

	"imagezero/internal/data/ent"
	"imagezero/internal/data/ent/payment"
	"imagezero/internal/data/ent/predicate"
	"imagezero/internal/data/schema"
	"imagezero/pkg/ecode"
	"imagezero/pkg/pager"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqljson"
)

type PaymentRepo struct {
	*Data
}

func NewPaymentRepo(data *Data) *PaymentRepo {
	return &PaymentRepo{Data: data}
}

func (repo *PaymentRepo) findOne(ctx context.Context, ps ...predicate.Payment) (*ent.Payment, error) {
	orders := []payment.OrderOption{
		ent.Desc(payment.FieldCreateTime),
	}
	data, err := repo.db.Payment.Query().
		Where(ps...).
		Order(orders...).
		First(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, ecode.NotFound
		}
		return nil, errors.Join(err, errors.New("data.payment.findOne"))
	}
	return data, nil
}

func (repo *PaymentRepo) Create(ctx context.Context, data *ent.Payment) (*ent.Payment, error) {
	u, err := repo.db.Payment.Create().
		SetUserID(data.UserID).
		SetPlanID(data.PlanID).
		SetPaymentMethod(data.PaymentMethod).
		SetSubID(data.SubID).
		SetStatus(data.Status).
		SetPayStatus(data.PayStatus).
		SetDueTime(data.DueTime).
		SetCreateTime(data.CreateTime).
		SetUpdateTime(data.UpdateTime).
		SetExtra(data.Extra).
		Save(ctx)
	if err != nil {
		return nil, errors.Join(err, errors.New("data.payment.create"))
	}
	return u, err
}

func (repo *PaymentRepo) CheckPayment(ctx context.Context, p *ent.Payment) {
	if p == nil {
		return
	}

	now := time.Now().Unix()
	if p.PayStatus != uint8(schema.PayStatusExpired) {
		if p.PaymentMethod == uint8(schema.PaymentMethodApple) {
			var extra = schema.PaymentExtra{}
			if p.Extra != "" {
				json.Unmarshal([]byte(p.Extra), &extra)
			}
			if extra.Env == "Sandbox" && (now-int64(p.DueTime) > 86400) {
				repo.PayExpired(ctx, p.SubID)
				p.PayStatus = uint8(schema.PayStatusExpired)
			}
			if extra.Env != "Sandbox" && (now-int64(p.DueTime) > 86400*5) {
			}
		} else if (p.PlanID > 20000 || strings.HasPrefix(p.SubID, "gift")) && (now-int64(p.DueTime)) > 3600 {
			p.PayStatus = uint8(schema.PayStatusExpired)
			repo.PayExpired(ctx, p.SubID)
		} else if (now - int64(p.DueTime)) > 86400*6 {
			p.PayStatus = uint8(schema.PayStatusExpired)
			repo.PayExpired(ctx, p.SubID)
		}
	}
}

func (repo *PaymentRepo) FindBySubID(ctx context.Context, subID string) (*ent.Payment, error) {
	p, e := repo.findOne(ctx, payment.SubID(subID))
	repo.CheckPayment(ctx, p)
	return p, e
}

func (repo *PaymentRepo) FindByUserID(ctx context.Context, userID uint) (*ent.Payment, error) {
	p, e := repo.findOne(ctx, payment.UserID(userID))
	repo.CheckPayment(ctx, p)
	return p, e
}

func (repo *PaymentRepo) FindStripePlanByUserID(ctx context.Context, userID uint, planID []uint) (*ent.Payment, error) {
	return repo.findOne(ctx, payment.UserID(userID), payment.PaymentMethod(uint8(schema.PaymentMethodStripe)), payment.PlanIDIn(planID...))
}

func (repo *PaymentRepo) PayCancel(ctx context.Context, subID string) (int, error) {
	return repo.db.Payment.Update().
		Where(payment.SubID(subID)).
		SetStatus(uint8(schema.SubscriptionStatusCancelled)).
		SetUpdateTime(uint64(time.Now().Unix())).
		Save(ctx)
}

func (repo *PaymentRepo) PayResume(ctx context.Context, subID string) (int, error) {
	return repo.db.Payment.Update().
		Where(payment.SubID(subID)).
		SetStatus(uint8(schema.SubscriptionStatusDefault)).
		SetUpdateTime(uint64(time.Now().Unix())).
		Save(ctx)
}

func (repo *PaymentRepo) PayTrialEnd(ctx context.Context, subID string, dueTime int64) (int, error) {
	return repo.db.Payment.Update().
		Where(payment.SubID(subID)).
		SetPayStatus(uint8(schema.PayStatusPaid)).
		SetDueTime(uint64(dueTime)).
		SetUpdateTime(uint64(time.Now().Unix())).
		Save(ctx)
}

func (repo *PaymentRepo) PayFailed(ctx context.Context, subID string) (int, error) {
	return repo.db.Payment.Update().
		Where(payment.SubID(subID)).
		SetPayStatus(uint8(schema.PayStatusPayFailed)).
		SetUpdateTime(uint64(time.Now().Unix())).
		Save(ctx)
}

func (repo *PaymentRepo) PayExpired(ctx context.Context, subID string) (int, error) {
	return repo.db.Payment.Update().
		Where(payment.SubID(subID)).
		SetStatus(uint8(schema.SubscriptionStatusCancelled)).
		SetPayStatus(uint8(schema.PayStatusExpired)).
		SetUpdateTime(uint64(time.Now().Unix())).
		Save(ctx)
}

func (repo *PaymentRepo) PayUpgrade(ctx context.Context, subID string, planID int, dueTime int64) (int, error) {
	return repo.db.Payment.Update().
		Where(payment.SubID(subID)).
		SetPlanID(uint(planID)).
		SetDueTime(uint64(dueTime)).
		SetUpdateTime(uint64(time.Now().Unix())).
		Save(ctx)
}

func (repo *PaymentRepo) UpdateExtra(ctx context.Context, paymentID int, extra string) (int, error) {
	return repo.db.Payment.Update().
		Where(payment.ID(paymentID)).
		SetExtra(extra).
		SetUpdateTime(uint64(time.Now().Unix())).
		Save(ctx)
}

func (repo *PaymentRepo) PayResubscribe(ctx context.Context, subID string, planID int, dueTime int64) (int, error) {
	return repo.db.Payment.Update().
		Where(payment.SubID(subID)).
		SetPlanID(uint(planID)).
		SetCreateTime(uint64(time.Now().Unix())).
		SetDueTime(uint64(dueTime)).
		SetStatus(uint8(schema.SubscriptionStatusDefault)).
		SetPayStatus(uint8(schema.PayStatusPaid)).
		SetUpdateTime(uint64(time.Now().Unix())).
		Save(ctx)
}

func (repo *PaymentRepo) PayRenew(ctx context.Context, subID string, dueTime int64) (int, error) {
	return repo.db.Payment.Update().
		Where(payment.SubID(subID)).
		SetPayStatus(uint8(schema.PayStatusPaid)).
		SetDueTime(uint64(dueTime)).
		SetUpdateTime(uint64(time.Now().Unix())).
		Save(ctx)
}

func (repo *PaymentRepo) SetDueTime(ctx context.Context, subID string, dueTime int64) (int, error) {
	return repo.db.Payment.Update().
		Where(payment.SubID(subID)).
		SetDueTime(uint64(dueTime)).
		SetUpdateTime(uint64(time.Now().Unix())).
		Save(ctx)
}

func (repo *PaymentRepo) TransferPlan(ctx context.Context, subID string, userID int) (int, error) {
	return repo.db.Payment.Update().
		Where(payment.SubID(subID)).
		SetUserID(uint(userID)).
		SetUpdateTime(uint64(time.Now().Unix())).
		Save(ctx)
}

func (repo *PaymentRepo) SetNewPlan(ctx context.Context, planID int, oldSubID string, subID string, dueTime int64, isCancelled bool, oldPlanCreateTime uint64) (int, error) {
	if isCancelled {
		return repo.db.Payment.Update().
			Where(payment.SubID(oldSubID)).
			SetPlanID(uint(planID)).
			SetSubID(subID).
			SetCreateTime(oldPlanCreateTime).
			SetStatus(uint8(schema.SubscriptionStatusCancelled)).
			SetPayStatus(uint8(schema.PayStatusPaid)).
			SetDueTime(uint64(dueTime)).
			SetUpdateTime(uint64(time.Now().Unix())).
			Save(ctx)
	} else {
		return repo.db.Payment.Update().
			Where(payment.SubID(oldSubID)).
			SetPlanID(uint(planID)).
			SetSubID(subID).
			SetCreateTime(oldPlanCreateTime).
			SetStatus(uint8(schema.SubscriptionStatusDefault)).
			SetPayStatus(uint8(schema.PayStatusPaid)).
			SetDueTime(uint64(dueTime)).
			SetUpdateTime(uint64(time.Now().Unix())).
			Save(ctx)
	}
}

func (repo *PaymentRepo) UpdatePlan(ctx context.Context, planID int, subID string, dueTime int64, isCancelled bool, oldPlanCreateTime uint64) (int, error) {
	if subID == "" || dueTime <= 0 {
		return 0, nil
	}

	if isCancelled {
		return repo.db.Payment.Update().
			Where(payment.SubID(subID)).
			SetPlanID(uint(planID)).
			SetCreateTime(oldPlanCreateTime).
			SetStatus(uint8(schema.SubscriptionStatusCancelled)).
			SetPayStatus(uint8(schema.PayStatusPaid)).
			SetDueTime(uint64(dueTime)).
			SetUpdateTime(uint64(time.Now().Unix())).
			Save(ctx)
	} else {
		return repo.db.Payment.Update().
			Where(payment.SubID(subID)).
			SetPlanID(uint(planID)).
			SetCreateTime(oldPlanCreateTime).
			SetStatus(uint8(schema.SubscriptionStatusDefault)).
			SetPayStatus(uint8(schema.PayStatusPaid)).
			SetDueTime(uint64(dueTime)).
			SetUpdateTime(uint64(time.Now().Unix())).
			Save(ctx)
	}
}

func (repo *PaymentRepo) ListAllPaidUsers(ctx context.Context, pager *pager.Pager) ([]*ent.Payment,
	error) {
	return repo.getList(ctx, pager, []payment.OrderOption{ent.Asc(payment.FieldID)}, payment.PayStatusNEQ(uint8(schema.PayStatusExpired)))
}

func (repo *PaymentRepo) ListAllCancelledUsers(ctx context.Context, pager *pager.Pager) ([]*ent.Payment,
	error) {
	return repo.getList(ctx, pager, []payment.OrderOption{ent.Asc(payment.FieldID)}, payment.PayStatusNEQ(uint8(schema.PayStatusExpired)), payment.StatusEQ(uint8(schema.SubscriptionStatusCancelled)), payment.PlanIDIn(1261, 1262, 1263, 1202))
}

func (repo *PaymentRepo) GetAppleInvalidPaymentList(ctx context.Context) ([]*ent.Payment, error) {
	now := time.Now().Unix()
	query := repo.db.Payment.Query().
		Where(
			payment.DueTimeLT(uint64(now-86400*3)),
			payment.PaymentMethodEQ(uint8(schema.PaymentMethodApple)),
			payment.PayStatusNEQ(uint8(schema.PayStatusExpired)))
	s, err := query.All(ctx)
	if err != nil {
		return nil, err
	}
	return s, nil
}

func (repo *PaymentRepo) IOSSubscriptionCount(ctx context.Context, userID int) (int, error) {
	n, err := repo.db.Payment.Query().
		Where(payment.UserIDEQ(uint(userID))).
		Where(payment.PaymentMethodEQ(uint8(schema.PaymentMethodApple))).
		Where(payment.Or(repo.paymentExtraTriggerEQ("app_ios"),
			payment.PlanIDIn(1401, 1402, 1403, 1404, 1405, 1406, 1420, 1421)),
			repo.paymentExtraProductIDContains("ChitChat_")).
		Count(ctx)
	if err != nil {
		return 0, errors.Join(err, errors.New("data.payment.count"))
	}
	return n, nil
}

func (repo *PaymentRepo) paymentExtraTriggerEQ(tg string) predicate.Payment {
	return func(s *sql.Selector) {
		s.Where(sqljson.ValueEQ(payment.FieldExtra, tg, sqljson.Path("trigger")))
	}
}

func (repo *PaymentRepo) paymentExtraProductIDContains(productID string) predicate.Payment {
	return func(s *sql.Selector) {
		s.Where(sqljson.ValueContains(payment.FieldExtra, productID, sqljson.Path("productID")))
	}
}

func (repo *PaymentRepo) getList(ctx context.Context, pager *pager.Pager, order []payment.OrderOption,
	ps ...predicate.Payment) ([]*ent.Payment, error) {
	var err error
	if pager != nil {
		pager.Total, err = repo.count(ctx, ps...)
		if err != nil {
			return nil, err
		}
	}
	query := repo.db.Payment.Query().
		Where(ps...).
		Order(order...)

	if pager != nil {
		query.Offset(pager.Offset()).Limit(pager.Limit())
	}
	s, err := query.All(ctx)
	if err != nil {
		return nil, errors.Join(err, errors.New("data.payment.getList"))
	}
	return s, nil
}

func (r *PaymentRepo) count(ctx context.Context, ps ...predicate.Payment) (int, error) {
	n, err := r.db.Payment.Query().
		Where(ps...).
		Count(ctx)
	if err != nil {
		return 0, errors.Join(err, errors.New("data.payment.count"))
	}
	return n, nil
}
