package data

import (
	"context"
	"encoding/json"
	"errors"
	"time"

	"imagezero/internal/data/ent"
	"imagezero/internal/data/ent/paymentlogs"
	paymentLogsEnt "imagezero/internal/data/ent/paymentlogs"
	"imagezero/internal/data/ent/predicate"
	"imagezero/internal/data/schema"
	"imagezero/pkg/ecode"
)

type PaymentLogsRepo struct {
	*Data
}

func NewPaymentLogsRepo(data *Data) *PaymentLogsRepo {
	return &PaymentLogsRepo{Data: data}
}

func (repo *PaymentLogsRepo) CreateLog(ctx context.Context, logType schema.PaymentLogType, userID uint, objID *string, extra map[string]interface{}) (*ent.PaymentLogs, error) {
	var data = new(ent.PaymentLogs)
	data.UserID = userID
	data.Type = uint(logType)
	data.CreateTime = uint64(time.Now().Unix())
	data.Extra = ""
	if extra != nil {
		extraData, _ := json.Marshal(extra)
		data.Extra = string(extraData)
	}
	if objID == nil {
		data.ObjID = ""
	} else {
		data.ObjID = *objID
	}
	return repo.create(ctx, repo.db, data)
}

func (repo *PaymentLogsRepo) create(ctx context.Context, db *ent.Client, data *ent.PaymentLogs) (*ent.PaymentLogs, error) {
	data, err := db.PaymentLogs.Create().
		SetUserID(data.UserID).
		SetObjID(data.ObjID).
		SetType(data.Type).
		SetCreateTime(data.CreateTime).
		SetExtra(data.Extra).
		Save(ctx)
	if err != nil {
		return nil, errors.Join(err, errors.New("data.payment.create"))
	}
	return data, err
}

func (repo *PaymentLogsRepo) FindByUserIDAndObjID(ctx context.Context, logType schema.PaymentLogType, userID uint, objID string) (*ent.PaymentLogs, error) {
	return repo.findOne(ctx, paymentLogsEnt.UserID(userID), paymentLogsEnt.Type(uint(logType)), paymentLogsEnt.ObjID(objID))
}

func (repo *PaymentLogsRepo) FindByObjID(ctx context.Context, logType schema.PaymentLogType, objID string) (*ent.PaymentLogs, error) {
	return repo.findOne(ctx, paymentLogsEnt.Type(uint(logType)), paymentLogsEnt.ObjID(objID))
}

func (repo *PaymentLogsRepo) CountByObjID(ctx context.Context, logType schema.PaymentLogType, objID string) (int, error) {
	return repo.count(ctx, paymentLogsEnt.Type(uint(logType)), paymentLogsEnt.ObjID(objID))
}

func (repo *PaymentLogsRepo) FindByUserID(ctx context.Context, logType schema.PaymentLogType, userID uint) (*ent.PaymentLogs, error) {
	return repo.findOne(ctx, paymentLogsEnt.Type(uint(logType)), paymentLogsEnt.UserID(userID))
}

func (repo *PaymentLogsRepo) UpdateLinkPlanLog(ctx context.Context, objID string) (int, error) {
	return repo.db.PaymentLogs.Update().
		Where(paymentlogs.ObjID(objID)).
		Where(paymentlogs.Type(uint(schema.LinkPlan))).
		SetType(uint(schema.UnLinkPlan)).
		Save(ctx)
}

func (repo *PaymentLogsRepo) findOne(ctx context.Context, ps ...predicate.PaymentLogs) (*ent.PaymentLogs, error) {
	orders := []paymentlogs.OrderOption{
		ent.Desc(paymentlogs.FieldID),
	}
	data, err := repo.db.PaymentLogs.Query().
		Where(ps...).
		Order(orders...).
		First(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, ecode.NotFound
		}
		return nil, errors.Join(err, errors.New("data.paymentlogs.findOne"))
	}
	return data, nil
}

func (repo *PaymentLogsRepo) count(ctx context.Context, ps ...predicate.PaymentLogs) (int, error) {
	n, err := repo.db.PaymentLogs.Query().
		Where(ps...).
		Count(ctx)
	if err != nil {
		return 0, errors.Join(err, errors.New("data.paymentlogs.count"))
	}

	return n, nil
}
