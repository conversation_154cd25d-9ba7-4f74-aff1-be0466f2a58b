package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
)

type PaymentPlan struct {
	ent.Schema
}

func (PaymentPlan) Fields() []ent.Field {
	return []ent.Field{
		field.Int("id").StorageKey("id"),
		field.String("price_id").StorageKey("price_id"),
		field.String("product_id").StorageKey("product_id"),
		field.String("name").StorageKey("name"),
		field.String("display_name").StorageKey("display_name"),
		field.Int("price").StorageKey("price"),
		field.Int("origin_price").StorageKey("origin_price"),
		field.String("currency").StorageKey("currency"),
		field.String("description").StorageKey("description"),
		field.String("interval").StorageKey("interval"),
		field.String("iap_product_id").Default("").StorageKey("iap_product_id"),
		field.String("mac_iap_product_id").Default("").StorageKey("mac_iap_product_id"),
	}
}

func (PaymentPlan) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "payment_plans"},
	}
}
