package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
)

type PaymentMethod uint

const (
	PaymentMethodStripe PaymentMethod = 0
	PaymentMethodPaypal PaymentMethod = 1
	PaymentMethodApple  PaymentMethod = 2
	PaymentMethodGoogle PaymentMethod = 3
)

type SubscriptionStatus uint

const (
	SubscriptionStatusDefault   SubscriptionStatus = 0
	SubscriptionStatusCancelled SubscriptionStatus = 1
)

type PayStatus uint

const (
	PayStatusDefault   PayStatus = 0
	PayStatusPaid      PayStatus = 1
	PayStatusPayFailed PayStatus = 2
	PayStatusExpired   PayStatus = 3
	PayStatusTrial     PayStatus = 4
)

type Payment struct {
	ent.Schema
}

type PaymentExtra struct {
	CustomerID      string `json:"customerID"`
	PaymentMethod   string `json:"paymentMethod"`
	Price           int    `json:"price"`
	Currency        string `json:"currency"`
	ResubscribeTime int64  `json:"resubscribeTime"`
	UpgradeTime     int64  `json:"upgradeTime"`
	IsSubscription  bool   `json:"isSubscription"`
	Env             string `json:"env"`
	IP              string `json:"ip"`
	Region          string `json:"region"`
	ProductID       string `json:"productID"`
	Trigger         string `json:"trigger"`
	PurchaseToken   string `json:"purchase_token"`
	OrderID         string `json:"order_id"`
	OldSubID        string `json:"old_sub_id"`
}

func (Payment) Fields() []ent.Field {
	return []ent.Field{
		field.Uint("user_id"),
		field.Uint("plan_id"),
		field.Uint8("payment_method").Default(0),
		field.String("sub_id").NotEmpty(),
		field.Uint8("status").Default(0),
		field.Uint8("pay_status").Default(0),
		field.Uint64("due_time"),
		field.Uint64("create_time"),
		field.Uint64("update_time"),
		field.String("extra").Default(""),
	}
}

func (Payment) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "payments"},
	}
}
