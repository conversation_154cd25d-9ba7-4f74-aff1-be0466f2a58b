package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"

	"imagezero/pkg/entutil"
)

var (
	AppNames = []string{"global", "sider_mac", "sider_ios", "sider_chrome",
		"sider_edge", "sider_web", "sider_windows", "ChitChat_Chrome_Ext", "ChitChat_Edge_Ext",
		"ChitChat_iOS", "ChitChat_Mac", "ChitChat_Windows", "ChitChat_Web", "ChitChat_Android",
		"ChitChat_Safari_Ext", "sider_electron_windows", "sider_electron_mac", "sider_electron_linux",
		"picture_question_iOS", "sider_android", "Client_Finding_Jobs_Windows", "Client_Finding_Jobs_Mac",
		"library_web",
	}
	MobileAppNames   = []string{"sider_ios", "ChitChat_iOS", "ChitChat_Android", "sider_android"}
	AndroidsAppNames = []string{"ChitChat_Android", "sider_android"}
)

// User holds the schema definition for the User entity.
type User struct {
	ent.Schema
}

func (User) Mixin() []ent.Mixin {
	return []ent.Mixin{
		entutil.TimeWithDelete{},
	}
}

// Fields of the User.
func (User) Fields() []ent.Field {
	return []ent.Field{
		field.String("nickname").Default(""),
		field.Enum("register_type").Values("device", "oauth2", "email", "phone", "device-transfer").Default("device"),
		field.String("device_id"),
		field.String("register_ip").NotEmpty(),
		field.String("register_region").Default(""),
		field.String("email").Default(""),
		field.String("phone").Default(""),
		field.Bool("email_verified").Default(false),
		field.String("password").Default(""),
		field.Enum("register_app_name").Values(AppNames...),
		field.String("register_app_version").Default(""),
		field.String("avatar").Default(""),
		field.String("profile").Default(""),
		field.String("register_from").Default(""),
		field.Time("bind_at").Nillable().Optional().SchemaType(map[string]string{
			dialect.Postgres: "datetime",
		}),
		field.Int("transfer_user_id").Default(0),
		field.Bool("blocked").Default(false),
		field.JSON("extra", map[string]any{}).Optional(),
	}
}

//// Edges of the User.
//func (User) Edges() []ent.Edge {
//	return []ent.Edge{
//		edge.To("completion_logs", CompletionLog.Type),
//		edge.To("oauth", UserOAuth.Type),
//		edge.To("share_gpt", ShareGPT.Type),
//		edge.To("gpt_conversations", GPTConversation.Type),
//		edge.To("embedding", Embedding.Type),
//		edge.To("user_setting", UserSetting.Type),
//		edge.To("query_quota", UserQueryQuota.Type),
//		edge.To("user_login_log", UserLoginLog.Type),
//		edge.To("ocr", OCR.Type),
//		edge.To("feedback", Feedback.Type),
//	}
//}

func (User) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("device_id"),
		index.Fields("register_type"),
		index.Fields("register_ip"),
		index.Fields("email"),
		index.Fields("phone"),
		index.Fields("register_app_name"),
	}
}
