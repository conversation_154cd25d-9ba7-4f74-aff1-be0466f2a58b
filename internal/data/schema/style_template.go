package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/field"
	"github.com/Sider-ai/go-pkg/entutil"
)

type StyleTemplate struct {
	ent.Schema
}

func (StyleTemplate) Mixin() []ent.Mixin {
	return []ent.Mixin{
		entutil.TimeWithDelete{},
	}
}

func (StyleTemplate) Fields() []ent.Field {
	return []ent.Field{
		field.String("id").
			Unique().
			Immutable().
			Comment("Unique identifier for the template"),

		field.Strings("url").
			Comment("Remote resource URLs for the template"),

		field.String("prompt").
			Optional().
			Comment("Optional AI prompt used to generate images"),

		field.String("template_type").
			Comment("Type of the template"),

		field.Int("width").
			Optional().
			Comment("Width of the template image"),

		field.Int("height").
			Optional().
			Comment("Height of the template image"),

		field.String("local_image_name").
			Optional().
			Comment("Local image filename"),

		field.String("video_url").
			Optional().
			Comment("URL to the template video"),
	}
}
