package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
)

// UserRequest holds the schema definition for the UserRequest entity.
type UserRequest struct {
	ent.Schema
}

// Fields of the UserRequest.
func (UserRequest) Fields() []ent.Field {
	return []ent.Field{
		field.String("trace_id").
			NotEmpty().
			Comment("Unique trace ID for the request"),

		field.Int("user_id"),

		field.String("method").
			NotEmpty().
			Comment("HTTP method of the request"),

		field.String("path").
			NotEmpty().
			Comment("Request path"),

		field.Enum("status").
			Values("pending", "completed", "failed").
			Default("pending").
			SchemaType(map[string]string{
				dialect.MySQL:    "varchar(64)",
				dialect.Postgres: "varchar(128)",
			}).
			Comment("Current status of the request"),

		field.Int("status_code").
			Optional().
			Comment("HTTP status code of the response"),

		field.Text("req_data").
			Optional().
			Comment("Request data including headers"),

		field.Text("resp_data").
			Optional().
			Comment("Response data"),

		field.String("error_message").
			Optional().
			Comment("Error message if the request failed"),

		field.Time("received_at").
			SchemaType(map[string]string{
				dialect.MySQL:    "timestamp(3)",
				dialect.Postgres: "timestamp(3) with time zone",
			}).
			Default(time.Now).
			Comment("Time when the request was received"),

		field.Time("responded_at").
			SchemaType(map[string]string{
				dialect.MySQL:    "timestamp(3)",
				dialect.Postgres: "timestamp(3) with time zone",
			}).
			Optional().
			Comment("Time when the response was sent"),

		field.Int64("processing_time").
			Optional().
			Comment("Time taken to process the request in nanoseconds"),

		field.String("app_name").
			Default("").
			SchemaType(map[string]string{
				dialect.MySQL:    "varchar(128)",
				dialect.Postgres: "varchar(128)",
			}),

		field.String("app_version").
			Default("").
			SchemaType(map[string]string{
				dialect.MySQL:    "varchar(128)",
				dialect.Postgres: "varchar(128)",
			}),

		field.String("user_ip").
			Default("").
			SchemaType(map[string]string{
				dialect.MySQL:    "varchar(128)",
				dialect.Postgres: "varchar(128)",
			}),

		field.String("user_region").
			Default("").
			SchemaType(map[string]string{
				dialect.MySQL:    "varchar(32)",
				dialect.Postgres: "varchar(32)",
			}),

		field.String("service_name").
			Default("").
			SchemaType(map[string]string{
				dialect.MySQL:    "varchar(128)",
				dialect.Postgres: "varchar(128)",
			}),

		field.String("service_function").
			Default("").
			SchemaType(map[string]string{
				dialect.MySQL:    "varchar(128)",
				dialect.Postgres: "varchar(128)",
			}),
	}
}

// Edges of the UserRequest.
func (UserRequest) Edges() []ent.Edge {
	return nil
}

// Indexes of the UserRequest.
func (UserRequest) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("trace_id").
			Unique(),
		index.Fields("user_id"),
		index.Fields("received_at"),
	}
}
