package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
)

type PaymentLogType uint

const (
	CreateNewSessionSuccess          PaymentLogType = 1001
	CreateNewSessionFailed           PaymentLogType = 1002
	CreateSubscriptionSuccess        PaymentLogType = 1003
	CreateSubscriptionFailed         PaymentLogType = 1004
	InvalidReceiptData               PaymentLogType = 1005
	ReceiptVerify                    PaymentLogType = 1006
	AlreadyPaid                      PaymentLogType = 1007
	LinkPlan                         PaymentLogType = 1008
	TransferPlan                     PaymentLogType = 1009
	HandleNotification               PaymentLogType = 1010
	InvalidDevice                    PaymentLogType = 1011
	LoginTransferPlan                PaymentLogType = 1012
	UnLinkPlan                       PaymentLogType = 1013
	PremiumReward                    PaymentLogType = 1014
	UpgradeReward                    PaymentLogType = 1015
	UpgradePlan                      PaymentLogType = 1016
	CreateUpgradeSessionFailed       PaymentLogType = 1017
	CreateUpgradeSessionSuccess      PaymentLogType = 1018
	FreeTrialSetupFailed             PaymentLogType = 1019
	FreeTrialSetupSuccess            PaymentLogType = 1020
	FreeTrialSetupTimeout            PaymentLogType = 1021
	FreeTrialCreateFailed            PaymentLogType = 1022
	FreeTrialEnd                     PaymentLogType = 1023
	ApplePaymentCheck                PaymentLogType = 1024
	CreateQuotaPaymentSessionFailed  PaymentLogType = 1025
	CreateQuotaPaymentSessionSuccess PaymentLogType = 1026
	QuotaPaymentChargeFailed         PaymentLogType = 1027
	QuotaPaymentChargeSuccess        PaymentLogType = 1028
	SetNewPlan                       PaymentLogType = 1029
	InvalidProductID                 PaymentLogType = 1030
	OneTimePaymentDispute            PaymentLogType = 1031
	BlackFridayPromotion2024         PaymentLogType = 1033
	XmasPromotion2024                PaymentLogType = 1034
	BlackFridayIntroductory2024      PaymentLogType = 1035
	XmasIntroductory2024             PaymentLogType = 1036
	CancelPlan                       PaymentLogType = 1040
	CreateImpactConversion           PaymentLogType = 1051
	AndroidSubscriptionVerify        PaymentLogType = 1041
	AndroidSubscriptionCreate        PaymentLogType = 1042
	AndroidSubscriptionUpdate        PaymentLogType = 1043
	AndroidSubscriptionExpired       PaymentLogType = 1044
	AndroidSubscriptionNotification  PaymentLogType = 1045
	AndroidSubscriptionCheckUpdate   PaymentLogType = 1046
	AndroidPurchaseVerify            PaymentLogType = 1047
)

type PaymentLogs struct {
	ent.Schema
}

func (PaymentLogs) Fields() []ent.Field {
	return []ent.Field{
		field.Uint("user_id"),
		field.String("obj_id"),
		field.Uint("type"),
		field.Uint64("create_time"),
		field.String("extra").Default(""),
	}
}

func (PaymentLogs) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "payment_logs"},
	}
}
