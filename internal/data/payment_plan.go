package data

import (
	"context"
	"errors"

	"imagezero/configs"
	"imagezero/internal/data/ent"
	"imagezero/internal/data/ent/paymentplan"
	"imagezero/internal/data/ent/predicate"
	"imagezero/pkg/ecode"
)

type PaymentPlanRepo struct {
	*Data
}

func NewPaymentPlanRepo(data *Data) *PaymentPlanRepo {
	return &PaymentPlanRepo{Data: data}
}

func (repo *PaymentPlanRepo) findOne(ctx context.Context, ps ...predicate.PaymentPlan) (*configs.PaymentPlan, error) {
	orders := []paymentplan.OrderOption{
		ent.Desc(paymentplan.FieldID),
	}
	data, err := repo.db.PaymentPlan.Query().
		Where(ps...).
		Order(orders...).
		First(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, ecode.NotFound
		}
		return nil, errors.Join(err, errors.New("data.paymentPlan.findOne"))
	}

	return repo.Format(data), nil
}

func (repo *PaymentPlanRepo) Format(p *ent.PaymentPlan) *configs.PaymentPlan {
	if p == nil {
		return nil
	}

	return &configs.PaymentPlan{
		ID:              p.ID,
		PriceID:         p.PriceID,
		ProductID:       p.ProductID,
		Name:            p.Name,
		DisplayName:     p.DisplayName,
		Price:           p.Price,
		Currency:        p.Currency,
		OriginPrice:     p.OriginPrice,
		Description:     p.Description,
		Interval:        p.Interval,
		IAPProductID:    p.IapProductID,
		MACIAPProductID: p.MACIapProductID,
	}
}

func (repo *PaymentPlanRepo) GetPlanByPlanID(ctx context.Context, planID int) (*configs.PaymentPlan, error) {
	return repo.findOne(ctx, paymentplan.ID(planID))
}

func (repo *PaymentPlanRepo) GetPlanByProductID(ctx context.Context, productID string) (*configs.PaymentPlan, error) {
	return repo.findOne(ctx, paymentplan.ProductIDEQ(productID))
}

func (repo *PaymentPlanRepo) GetProductIDByPlanID(ctx context.Context, planID int) string {
	plan, _ := repo.GetPlanByPlanID(ctx, planID)
	if plan == nil {
		return ""
	} else {
		return plan.ProductID
	}
}

func (repo *PaymentPlanRepo) ListAllPlan(ctx context.Context, planIDList []int) ([]*configs.PaymentPlan,
	error) {
	return repo.getList(ctx, []paymentplan.OrderOption{ent.Asc(paymentplan.FieldID)}, paymentplan.IDIn(planIDList...))
}

func (repo *PaymentPlanRepo) getList(ctx context.Context, order []paymentplan.OrderOption,
	ps ...predicate.PaymentPlan) ([]*configs.PaymentPlan, error) {
	var err error

	query := repo.db.PaymentPlan.Query().
		Where(ps...).
		Order(order...)

	s, err := query.All(ctx)
	if err != nil {
		return nil, errors.Join(err, errors.New("data.paymentPlan.getList"))
	}

	var ret []*configs.PaymentPlan
	for _, p := range s {
		ret = append(ret, repo.Format(p))
	}
	return ret, nil
}
