package data

import (
	"context"
	"time"

	"imagezero/internal/data/ent"
	"imagezero/internal/data/ent/userrequest"
)

type RequestContext struct {
	AppName         string
	AppVersion      string
	UserIP          string
	UserRegion      string
	ServiceName     string
	ServiceFunction string
}

type UserRequestRepo struct {
	data *Data
}

func NewUserRequestRepo(data *Data) *UserRequestRepo {
	return &UserRequestRepo{
		data: data,
	}
}

// CreateInitialRequest 创建初始状态的请求记录
func (r *UserRequestRepo) CreateInitialRequest(ctx context.Context, traceID string, userID int, method, path, reqData string, reqCtx RequestContext) (*ent.UserRequest, error) {
	return r.data.db.UserRequest.
		Create().
		SetTraceID(traceID).
		SetUserID(userID).
		SetMethod(method).
		SetPath(path).
		SetReqData(reqData).
		SetStatus(userrequest.StatusPending).
		SetReceivedAt(time.Now()).
		SetAppName(reqCtx.AppName).
		SetAppVersion(reqCtx.AppVersion).
		SetUserIP(reqCtx.UserIP).
		SetUserRegion(reqCtx.UserRegion).
		SetServiceName(reqCtx.ServiceName).
		SetServiceFunction(reqCtx.ServiceFunction).
		Save(ctx)
}

// RequestCompleted 将请求标记为已完成并更新相关字段
func (r *UserRequestRepo) RequestCompleted(ctx context.Context, traceID string, statusCode int, respData string) error {
	now := time.Now()
	req, err := r.data.db.UserRequest.
		Query().
		Where(userrequest.TraceID(traceID)).
		Only(ctx)
	if err != nil {
		return err
	}

	_, err = req.Update().
		SetStatus(userrequest.StatusCompleted).
		SetStatusCode(statusCode).
		SetRespData(respData).
		SetRespondedAt(now).
		SetProcessingTime(int64(now.Sub(req.ReceivedAt))).
		Save(ctx)

	return err
}

// RequestFailed 将请求标记为失败并更新相关字段
func (r *UserRequestRepo) RequestFailed(ctx context.Context, traceID string, statusCode int, errorMessage string) error {
	now := time.Now()
	req, err := r.data.db.UserRequest.
		Query().
		Where(userrequest.TraceID(traceID)).
		Only(ctx)
	if err != nil {
		return err
	}

	_, err = req.Update().
		SetStatus(userrequest.StatusFailed).
		SetStatusCode(statusCode).
		SetErrorMessage(errorMessage).
		SetRespondedAt(now).
		SetProcessingTime(int64(now.Sub(req.ReceivedAt))).
		Save(ctx)

	return err
}

// GetRequestByTraceID 通过 TraceID 查找特定记录
func (r *UserRequestRepo) GetRequestByTraceID(ctx context.Context, traceID string) (*ent.UserRequest, error) {
	return r.data.db.UserRequest.
		Query().
		Where(userrequest.TraceID(traceID)).
		Only(ctx)
}

// GetRequestByID 通过 ID 查找特定记录
func (r *UserRequestRepo) GetRequestByID(ctx context.Context, id int) (*ent.UserRequest, error) {
	return r.data.db.UserRequest.
		Get(ctx, id)
}
