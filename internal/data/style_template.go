package data

import (
	"context"
	"time"

	"imagezero/internal/data/ent"
	"imagezero/internal/data/ent/styletemplate"
)

// StyleTemplateRepo 是对样式模板仓库的实现
type StyleTemplateRepo struct {
	*Data
}

// NewStyleTemplateRepo 创建一个新的StyleTemplateRepo实例
func NewStyleTemplateRepo(data *Data) *StyleTemplateRepo {
	return &StyleTemplateRepo{Data: data}
}

// Create 创建一个新的StyleTemplate
func (repo *StyleTemplateRepo) Create(ctx context.Context, template *ent.StyleTemplate) (*ent.StyleTemplate, error) {
	builder := repo.db.StyleTemplate.Create().
		SetID(template.ID).
		SetURL(template.URL).
		SetTemplateType(template.TemplateType)

	if template.Prompt != "" {
		builder.SetPrompt(template.Prompt)
	}
	if template.Width != 0 {
		builder.SetWidth(template.Width)
	}
	if template.Height != 0 {
		builder.SetHeight(template.Height)
	}
	if template.LocalImageName != "" {
		builder.SetLocalImageName(template.LocalImageName)
	}
	if template.VideoURL != "" {
		builder.SetVideoURL(template.VideoURL)
	}

	result, err := builder.Save(ctx)
	return result, repo.warpError(err)
}

// Update 更新既有的StyleTemplate
func (repo *StyleTemplateRepo) Update(ctx context.Context, template *ent.StyleTemplate) (*ent.StyleTemplate, error) {
	update := repo.db.StyleTemplate.UpdateOneID(template.ID).
		SetURL(template.URL).
		SetTemplateType(template.TemplateType)

	if template.Prompt != "" {
		update.SetPrompt(template.Prompt)
	} else {
		update.ClearPrompt()
	}

	if template.Width != 0 {
		update.SetWidth(template.Width)
	} else {
		update.ClearWidth()
	}

	if template.Height != 0 {
		update.SetHeight(template.Height)
	} else {
		update.ClearHeight()
	}

	if template.LocalImageName != "" {
		update.SetLocalImageName(template.LocalImageName)
	} else {
		update.ClearLocalImageName()
	}

	if template.VideoURL != "" {
		update.SetVideoURL(template.VideoURL)
	} else {
		update.ClearVideoURL()
	}

	result, err := update.Save(ctx)
	return result, repo.warpError(err)
}

// Get 通过ID获取StyleTemplate
func (repo *StyleTemplateRepo) Get(ctx context.Context, id string) (*ent.StyleTemplate, error) {
	result, err := repo.db.StyleTemplate.Query().
		Where(styletemplate.ID(id)).
		Where(styletemplate.DeleteTimeIsNil()).
		Only(ctx)

	return result, repo.warpError(err)
}

// List 获取所有符合条件的StyleTemplate
func (repo *StyleTemplateRepo) List(ctx context.Context, opts ...StyleTemplateOption) ([]*ent.StyleTemplate, error) {
	query := repo.db.StyleTemplate.Query().Where(styletemplate.DeleteTimeIsNil())

	for _, opt := range opts {
		query = opt(query)
	}

	result, err := query.All(ctx)
	return result, repo.warpError(err)
}

// ListPage 根据每页数量和当前页码进行分页查询
func (repo *StyleTemplateRepo) ListPage(ctx context.Context, page, pageSize int, opts ...StyleTemplateOption) ([]*ent.StyleTemplate, error) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize

	query := repo.db.StyleTemplate.Query().
		Where(styletemplate.DeleteTimeIsNil()).
		Limit(pageSize).
		Offset(offset)

	for _, opt := range opts {
		query = opt(query)
	}

	result, err := query.All(ctx)
	return result, repo.warpError(err)
}

// Count 获取符合条件的StyleTemplate总数量
func (repo *StyleTemplateRepo) Count(ctx context.Context, opts ...StyleTemplateOption) (int, error) {
	query := repo.db.StyleTemplate.Query().Where(styletemplate.DeleteTimeIsNil())

	for _, opt := range opts {
		query = opt(query)
	}

	count, err := query.Count(ctx)
	return count, repo.warpError(err)
}

// Delete 软删除StyleTemplate
func (repo *StyleTemplateRepo) Delete(ctx context.Context, id string) error {
	now := time.Now()
	_, err := repo.db.StyleTemplate.UpdateOneID(id).
		SetDeleteTime(now).
		Save(ctx)

	return repo.warpError(err)
}

// ReplaceAll 删除所有现有的StyleTemplate并用新的替换
func (repo *StyleTemplateRepo) ReplaceAll(ctx context.Context, templates []*ent.StyleTemplate) ([]*ent.StyleTemplate, error) {
	// 开始事务
	tx, err := repo.db.Tx(ctx)
	if err != nil {
		return nil, repo.warpError(err)
	}

	// 创建回滚函数
	rollback := func(err error) ([]*ent.StyleTemplate, error) {
		if rerr := tx.Rollback(); rerr != nil {
			err = rerr
		}
		return nil, repo.warpError(err)
	}

	// 硬删除所有现有记录
	_, err = tx.StyleTemplate.Delete().Exec(ctx)
	if err != nil {
		return rollback(err)
	}

	// 创建所有新记录
	var created []*ent.StyleTemplate
	for _, template := range templates {
		builder := tx.StyleTemplate.Create().
			SetID(template.ID).
			SetURL(template.URL).
			SetTemplateType(template.TemplateType)

		if template.Prompt != "" {
			builder.SetPrompt(template.Prompt)
		}
		if template.Width != 0 {
			builder.SetWidth(template.Width)
		}
		if template.Height != 0 {
			builder.SetHeight(template.Height)
		}
		if template.LocalImageName != "" {
			builder.SetLocalImageName(template.LocalImageName)
		}
		if template.VideoURL != "" {
			builder.SetVideoURL(template.VideoURL)
		}

		result, err := builder.Save(ctx)
		if err != nil {
			return rollback(err)
		}
		created = append(created, result)
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return rollback(err)
	}

	return created, nil
}

// StyleTemplateOption 是用于配置StyleTemplate查询的选项
type StyleTemplateOption func(*ent.StyleTemplateQuery) *ent.StyleTemplateQuery

// WithTemplateType 按模板类型过滤
func WithTemplateType(templateType string) StyleTemplateOption {
	return func(q *ent.StyleTemplateQuery) *ent.StyleTemplateQuery {
		if templateType != "" {
			return q.Where(styletemplate.TemplateType(templateType))
		}
		return q
	}
}

// WithIDs 按ID列表过滤
func WithIDs(ids []string) StyleTemplateOption {
	return func(q *ent.StyleTemplateQuery) *ent.StyleTemplateQuery {
		if len(ids) > 0 {
			return q.Where(styletemplate.IDIn(ids...))
		}
		return q
	}
}
