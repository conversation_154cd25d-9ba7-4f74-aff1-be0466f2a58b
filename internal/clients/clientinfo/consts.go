package clientinfo

const (
	AppNameValidateKey = "AppNameValidation"
)

type ClientType string

const (
	ClientTypeWeb     ClientType = "web"
	ClientTypeExt     ClientType = "ext"
	ClientTypeIos     ClientType = "ios"
	ClientTypeMac     ClientType = "mac"
	ClientTypeWin     ClientType = "win"
	ClientTypeAndroid ClientType = "android"
)

func (t ClientType) IsiOS() bool {
	return t == ClientTypeIos
}

func (t ClientType) IsMac() bool {
	return t == ClientTypeMac
}

func (t ClientType) IsExt() bool {
	return t == ClientTypeExt
}

func (t ClientType) IsWeb() bool {
	return t == ClientTypeWeb
}

func (t ClientType) IsWin() bool {
	return t == ClientTypeWin
}

func (t ClientType) IsAndroid() bool {
	return t == ClientTypeAndroid
}

func (t ClientType) IsExtOrWeb() bool {
	return t.IsExt() || t.<PERSON>()
}

func (t ClientType) IsiOSOrMac() bool {
	return t.IsiOS() || t.Is<PERSON>()
}

func (t ClientType) IsMobile() bool {
	return t.IsiOS() || t.IsAndroid()
}
