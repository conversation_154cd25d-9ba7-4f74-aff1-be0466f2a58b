package hashid

import (
	"fmt"
	"testing"

	"imagezero/configs"
)

func TestEncode(t *testing.T) {
	config, err := configs.InitConfig()
	if err != nil {
		t.Error(err)
	}
	hashID := NewHashID(config)

	userID := 3029

	hash := hashID.User.EncodeNotE(userID)

	t.Log(hash)
}

func TestDectionary(t *testing.T) {
	config, err := configs.InitConfig()
	if err != nil {
		t.Error(err)
	}
	hashID := NewHashID(config)

	hashIDs := []string{"DE0G4OQBM9G", "DE0GKAQ8LL1", "DE0D60QBA2D", "DE0D5ZQ0L21", "DE0G3AQRJM1",
		"DE0G2OQ3OM1", "DE01B2Q0A3D", "DE01MAQ3OW1", "DE0DZKQ593D", "DE01E8QXN9D", "DE01Y4QLNVD", "DE01RAQ384G",
		"DE0G7OQOWAG", "DE0GWMQLVZ1", "DE0DNAQMOLG", "DE0DJAQVJVD", "DE0DO9QM0LG", "DE0G8ZQY30D", "DE0DV7QYO9G",
		"DE01A3QO8ZG", "DE0D0VQNBZG", "DE0GLAQWLKD"}

	ids := ""
	for _, hID := range hashIDs {
		ids = fmt.Sprintf("%s, %d", ids, hashID.DictionaryEntry.DecodeNotE(hID))
	}

	t.Log(ids)

}
