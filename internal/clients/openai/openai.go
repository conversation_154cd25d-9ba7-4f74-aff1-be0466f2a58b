package openai

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/sashabaranov/go-openai"

	"imagezero/internal/clients/serverconf"
)

type Client struct {
	*openai.Client
	httpClient *resty.Client
	key        string
}

func NewClient(srvConf *serverconf.ServerConf) *Client {
	httpClient := resty.New()
	httpClient.SetTimeout(time.Second * 300)

	var key string
	srvConf.MustGet("apikey_openai_1", &key)

	return &Client{
		httpClient: httpClient,
		key:        key,
		Client:     openai.NewClient(key),
	}
}

func (c *Client) Generate(ctx context.Context, req openai.ImageRequest) (resp openai.ImageResponse, err error) {

	req.Model = "gpt-image-1"

	resp, err = c.CreateImage(ctx, req)
	if err != nil {
		fmt.Printf("Image creation error: %v\n", err)
		return
	}

	return resp, nil
}

func (c *Client) EditImage(ctx context.Context, req ImageEditRequest) (resp openai.ImageResponse, err error) {
	prompt := ""
	if req.Prompt != "" {
		prompt = req.Prompt
	}

	switch req.Size {
	case "1024x1024", "1024x1536", "1536x1024", "auto", "":
	default:
		req.Size = "auto"
	}

	formdata := map[string]string{
		"model":  "gpt-image-1",
		"prompt": prompt,
	}
	if req.Model != "" {
		formdata["model"] = req.Model
	}
	if req.Size != "" {
		formdata["size"] = req.Size
	}

	HttpReq := c.httpClient.R().
		SetAuthToken(c.key).
		SetFormData(formdata)

	for _, img := range req.Image {
		HttpReq.SetFileReader("image[]", img.FileName, bytes.NewReader(img.Data))
	}
	HttpResp, err := HttpReq.Post("https://api.openai.com/v1/images/edits")
	if err != nil {
		return resp, err
	}
	if !HttpResp.IsSuccess() {
		return resp, fmt.Errorf("%v", HttpResp.String())
	}
	resp = openai.ImageResponse{}
	err = json.Unmarshal(HttpResp.Body(), &resp)
	if err != nil {
		return resp, err
	}

	return resp, nil
}
