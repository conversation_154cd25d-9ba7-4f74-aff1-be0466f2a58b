package openai

import (
	"context"
	"encoding/base64"
	"fmt"
	"os"
	"testing"

	"github.com/sasha<PERSON>nov/go-openai"

	"imagezero/configs"
	"imagezero/internal/clients/serverconf"
	"imagezero/internal/data"
)

var (
	ctx     = context.Background()
	srvConf *serverconf.ServerConf
	client  *Client
)

func init() {
	os.Chdir("../../..")
	os.Setenv("APP_MODE", "local")

	config, err := configs.InitConfig()
	if err != nil {
		panic("load config err: " + err.Error())
	}

	ent, err := data.NewEntClient(ctx, config)
	if err != nil {
		panic("new ent err: " + err.Error())
	}

	d, _, err := data.NewData(ent, nil)
	if err != nil {
		panic("new data err: " + err.Error())
	}
	appconfigRepo := data.NewAppConfigRepo(d)

	srvConf, err = serverconf.NewServerConf(ctx, appconfigRepo)

	client = NewClient(srvConf)
}

func TestRawGen(t *testing.T) {
	ctx := context.Background()
	img, err := client.CreateImage(ctx, openai.ImageRequest{
		Prompt: "Ghibli Style painting, Top view shows the clear blue sea water, sparkling, and a turtle is floating leisurely in the sea",
		Model:  "gpt-image-1",
	})

	// Decode base64 string to image data
	imageData, err := base64.StdEncoding.DecodeString(img.Data[0].B64JSON)
	if err != nil {
		t.Fatal(err)
	}

	// Write to file
	err = os.WriteFile("TestRawGen_image.png", imageData, 0644)
	if err != nil {
		t.Fatal(err)
	}

	t.Log("Image saved successfully as TestRawGen_image.png")
}

func TestGen(t *testing.T) {
	ctx := context.Background()
	img, err := client.generate(ctx, openai.ImageRequest{
		Prompt: "ghibli style painting, a black young man laughing dancing with a little white unicorn, with rainbow and sunshine behind",
	})

	if err != nil {
		t.Fatal(err)
	}

	imageData, err := base64.StdEncoding.DecodeString(img.Data[0].B64JSON)
	if err != nil {
		t.Fatal(err)
	}

	// Write to file
	err = os.WriteFile("TestGen_image.png", imageData, 0644)
	if err != nil {
		t.Fatal(err)
	}

	t.Log("Image saved successfully as TestGen_image.png")
}

func TestEdit(t *testing.T) {
	ctx := context.Background()

	fileData, err := os.ReadFile("/Users/<USER>/Downloads/WechatIMG1.jpg")
	if err != nil {
		fmt.Printf("无法读取文件: %v\n", err)
		return
	}
	fileData2, err := os.ReadFile("/Users/<USER>/Downloads/%E6%97%A5%E6%9C%AC%E4%BA%BA%E8%84%B1%E5%8F%A3%E7%A7%80%E5%B0%B1%E6%98%AF%E6%95%A2%E8%AF%B4%EF%BC%81%E8%87%AA%E8%BF%B0%E4%B8%AD%E5%9B%BD%E5%B7%A8%E5%A4%A7%E6%96%87%E5%8C%96%E5%86%B2%E5%87%BB%EF%BC%9A%E5%8E%9F%E6%9D%A5%E9%BB%84%E8%89%B2%E4%B8%8D%E8%….jpg")
	if err != nil {
		fmt.Printf("无法读取文件: %v\n", err)
		return
	}

	img, err := client.editImage(ctx, ImageEditRequest{
		ImageRequest: openai.ImageRequest{
			Model:  "gpt-image-1",
			Prompt: "Replace the face of the person in the second picture onto the first picture, then output in manga style illustration.",
		},
		Image: []*ReqImage{
			{
				Data:     fileData,
				FileName: "1.jpg",
			},
			{
				Data:     fileData2,
				FileName: "2.jpg",
			},
		},
	},
	)

	if err != nil {
		t.Fatal(err)
	}

	imageData, err := base64.StdEncoding.DecodeString(img.Data[0].B64JSON)
	if err != nil {
		t.Fatal(err)
	}

	// Write to file
	err = os.WriteFile("TestEdit_image.png", imageData, 0644)
	if err != nil {
		t.Fatal(err)
	}

	t.Log("Image saved successfully as TestEdit_image.png")
}
