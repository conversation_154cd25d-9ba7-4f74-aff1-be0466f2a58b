package openai

import (
	"github.com/sashabaranov/go-openai"
)

type ImageEditRequest struct {
	Image []*ReqImage `json:"image"`
	Mask  *ReqImage   `json:"mask"`
	openai.ImageRequest
}

type ReqImage struct {
	Data     []byte `json:"-"`
	FileName string `json:"file_name"`
	Weight   int8   `json:"weight"`
	Mime     string `json:"mime"`
	Width    int    `json:"width"`
	Height   int    `json:"height"`
}
