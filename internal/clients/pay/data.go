package pay

type PaymentPlatform string

const (
	PaymentPlatformStripe PaymentPlatform = "stripe"
	PaymentPlatformApple  PaymentPlatform = "apple"
	PaymentPlatformPaypal PaymentPlatform = "paypal"
	PaymentPlatformGoogle PaymentPlatform = "google"
)

const (
	IOSProductID        = "ios.pro.weekly"
	IOSProductIDMonthly = "ios.pro.monthly"
	IOSProductIDYearly  = "ios.pro.yearly"
)

const (
	PaymentAppleLinkUserKey = "payment:link:user:%d"
	PaymentFindByUserIDKey  = "Payment:FindByUserID:%d"
)

const IOSPlanGroupID = "21676375"
const MacPlanGroupID = "21676375"

var NewIOSProductIDList = []string{
	IOSProductID,
	IOSProductIDMonthly,
	IOSProductIDYearly,
}

var AllIOSProductIDList = []string{
	IOSProductID,
	IOSProductIDMonthly,
	IOSProductIDYearly,
}

var BoosterPackProductIDList = []string{}
