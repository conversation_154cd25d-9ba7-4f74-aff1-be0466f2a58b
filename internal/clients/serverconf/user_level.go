package serverconf

import (
	"time"

	"imagezero/internal/clients/clientinfo"
)

type UserLevelInfo struct {
	UserType      UserLevelType
	PaymentMethod string
	PaidUser      int
	PaidTime      time.Time
}

type LimitPeriod string

const (
	LimitPeriodTotal   LimitPeriod = "total"
	LimitPeriodDaily   LimitPeriod = "daily"
	LimitPeriodWeekly  LimitPeriod = "weekly"
	LimitPeriodMonthly LimitPeriod = "monthly"
)

type UserLevelType string

const (
	UserLevelTypeDevice             UserLevelType = "device"
	UserLevelTypeLogin              UserLevelType = "login"
	UserLevelTypePremium            UserLevelType = "premium"
	UserLevelTypePremiumMonthly     UserLevelType = "premium_monthly"
	UserLevelTypePremiumPro         UserLevelType = "premium_pro"
	UserLevelTypePremiumUltra       UserLevelType = "premium_ultra"
	UserLevelTypePremiumUltraTrial  UserLevelType = "premium_ultra_trial"
	UserLevelTypePremiumUltraMobile UserLevelType = "premium_ultra_mobile"
	UserLevelTypePremiumStarter     UserLevelType = "premium_starter"
)

func (l UserLevelType) IsPremium() bool {
	return !l.IsDevice() && !l.IsLogin()
}

func (l UserLevelType) IsDevice() bool {
	return l == UserLevelTypeDevice
}

func (l UserLevelType) IsLogin() bool {
	return l == UserLevelTypeLogin
}

func (l UserLevelType) RedisKey(clientInfo clientinfo.ClientInfo) string {
	switch l {
	case UserLevelTypeDevice:
		return string(UserLevelTypeDevice)
	case UserLevelTypeLogin:
		return string(UserLevelTypeLogin)
	case UserLevelTypePremium, UserLevelTypePremiumMonthly, UserLevelTypePremiumPro, UserLevelTypePremiumUltra,
		UserLevelTypePremiumStarter:
		return string(UserLevelTypePremium)
	case UserLevelTypePremiumUltraMobile:
		if clientInfo.GetClientType().IsMobile() {
			return string(UserLevelTypePremium)
		}
		return string(UserLevelTypeLogin)
	case UserLevelTypePremiumUltraTrial:
		return "premium_trial"
	}
	return string(UserLevelTypeLogin)
}

func (l UserLevelType) UserType() string {
	if l.IsPremium() {
		return string(UserLevelTypePremium)
	}
	return string(l)
}

func (l UserLevelType) UserTypeDetail() string {
	if l == UserLevelTypePremiumUltraTrial {
		return string(UserLevelTypePremiumUltra)
	}
	return string(l)
}

func (l UserLevelType) IsBasicPremium() bool {
	return l == UserLevelTypePremium
}

func (l UserLevelType) IsTrial() bool {
	return l == UserLevelTypePremiumUltraTrial
}

func (l UserLevelType) IsMobilePremium() bool {
	return l == UserLevelTypePremiumUltraMobile
}

func (l UserLevelType) IsUnlimited() bool {
	return l == UserLevelTypePremiumUltra
}

func (l UserLevelType) IsMobileUnlimited() bool {
	return l == UserLevelTypePremiumUltraMobile
}

type PremiumAwardLevel string

const (
	PremiumAwardMonthly    PremiumAwardLevel = "monthly"
	PremiumAwardQuarterly  PremiumAwardLevel = "quarterly"
	PremiumAwardHalfYearly PremiumAwardLevel = "half_yearly"
	PremiumAwardYearly     PremiumAwardLevel = "yearly"
)
