package logdbsync

import (
	"encoding/json"
	"fmt"

	"github.com/Sider-ai/go-pkg/xstrings"
	"github.com/spf13/cast"
	"go.uber.org/atomic"

	"imagezero/internal/data"
	"imagezero/internal/data/ent"
	"imagezero/internal/data/ent/applog"
)

type Client struct {
	Repo *data.AppLogRepo

	syncing  atomic.Bool
	logCh    chan *ent.AppLog
	logLimit int
	workers  []*worker
}

func NewClient(repo *data.AppLogRepo) *Client {
	c := &Client{
		Repo:     repo,
		logLimit: 1000,
		logCh:    make(chan *ent.AppLog, 1000),
	}
	for i := 0; i < 5; i++ {
		c.workers = append(c.workers, newWorker(c.logCh, repo, 10))
	}
	return c
}

func (c *Client) Write(p []byte) (n int, err error) {
	// channel 写满后直接返回
	if len(c.logCh) >= c.logLimit {
		return len(p), nil
	}

	log := &ent.AppLog{From: applog.FromLog}
	if err = json.Unmarshal(p, &log.Extra); err != nil {
		log.Level = "unknown"
		log.ErrMsg = xstrings.BytesToString(p)
		log.Extra = map[string]any{
			"unmarshal_err": err,
		}
	} else {
		log.Level = applog.Level(cast.ToString(log.Extra["level"]))
		log.ErrMsg = cast.ToString(log.Extra["msg"])
		log.UserID = cast.ToInt(log.Extra["user_id"])
		log.TraceID = cast.ToString(log.Extra["trace_id"])
		log.ServiceName = cast.ToString(log.Extra["service_name"])
		keyErr := cast.ToString(log.Extra["err"])
		if keyErr != "" {
			log.ErrMsg += fmt.Sprintf(" err: %s", keyErr)
		}
		delete(log.Extra, "level")
		delete(log.Extra, "msg")
		delete(log.Extra, "user_id")
		delete(log.Extra, "err")
	}

	c.logCh <- log
	return len(p), nil
}

func (c *Client) Sync() error {
	if c.syncing.Load() {
		return nil
	}

	c.syncing.Store(true)
	for _, w := range c.workers {
		w.writeDB()
	}
	c.syncing.Store(false)
	return nil
}
