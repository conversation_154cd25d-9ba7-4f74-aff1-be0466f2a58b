package logdbsync

import (
	"context"
	"sync"
	"time"

	"imagezero/internal/data"
	"imagezero/internal/data/ent"
)

type worker struct {
	sync.Mutex
	repo  *data.AppLogRepo
	logs  []*ent.AppLog
	logCh chan *ent.AppLog
	limit int
}

func newWorker(logCh chan *ent.AppLog, repo *data.AppLogRepo, limit int) *worker {
	w := &worker{
		repo:  repo,
		logs:  make([]*ent.AppLog, 0, limit),
		logCh: logCh,
		limit: limit,
	}
	go w.run()
	return w
}

func (w *worker) run() {
	for log := range w.logCh {
		w.add(log)
		if len(w.logs) >= w.limit {
			w.writeDB()
		}
	}
}

func (w *worker) add(log *ent.AppLog) {
	w.Lock()
	defer w.Unlock()

	w.logs = append(w.logs, log)
}

func (w *worker) writeDB() {
	w.Lock()
	defer w.Unlock()

	if len(w.logs) == 0 {
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	_ = w.repo.Creates(ctx, w.logs...)
	w.logs = w.logs[:0]
	cancel()
}
