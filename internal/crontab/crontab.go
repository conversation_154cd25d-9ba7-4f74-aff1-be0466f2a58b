package crontab

import (
	"context"
	"fmt"
	"os"
	"reflect"
	"time"

	"github.com/go-co-op/gocron/v2"
	"go.uber.org/zap"

	"imagezero/configs"
)

type Client struct {
	Options
	config    *configs.Config
	scheduler gocron.Scheduler
	logger    *zap.SugaredLogger
}

func NewClient(opt Options, config *configs.Config) (*Client, error) {
	s, err := gocron.NewScheduler()
	if err != nil {
		return nil, err
	}
	return &Client{
		Options:   opt,
		config:    config,
		scheduler: s,
		logger:    opt.Logger,
	}, nil
}

func (c *Client) Start() error {
	val := reflect.ValueOf(c)
	typ := val.Type()
	taskType := reflect.TypeOf((*gocron.Job)(nil)).Elem()
	errType := reflect.TypeOf((*error)(nil)).Elem()
	for i := 0; i < typ.NumMethod(); i++ {
		method := val.Method(i)
		methodType := method.Type()
		if methodType.NumOut() == 2 && methodType.Out(0).Implements(taskType) && methodType.Out(1).Implements(errType) {
			results := method.Call(nil)
			if len(results) == 2 {
				if err, ok := results[1].Interface().(error); ok && err != nil {
					return err
				}
			}
		}
	}

	c.scheduler.Start()
	return nil
}

func (c *Client) StartAsync() error {
	var (
		errFmt = "crontab.StartAsync error:%s"
		tasks  []func() (gocron.Job, error)
	)

	tasks = append(
		tasks,
		c.registerCleanImageFolderTask,
	)

	for _, task := range tasks {
		if _, err := task(); err != nil {
			return fmt.Errorf(errFmt, err)
		}
	}

	c.scheduler.Start()
	return nil
}

func (c *Client) Stop() {
	_ = c.scheduler.Shutdown()
}

func (c *Client) registerCleanImageFolderTask() (gocron.Job, error) {
	return c.scheduler.NewJob(
		gocron.DurationJob(time.Hour*2),
		gocron.NewTask(func() {
			// 获取前天的日期并格式化为YYYYMMDD
			dayBeforeYesterday := time.Now().AddDate(0, 0, -2)
			folderName := dayBeforeYesterday.Format("20060102")

			// 构建要删除的文件夹路径
			folderPath := fmt.Sprintf("%s/%s", c.config.ImageService.ResponseStoragePath, folderName)

			// 删除文件夹
			err := os.RemoveAll(folderPath)
			if err != nil {
				c.logger.Errorw("Failed to clean image folder",
					"folderPath", folderPath,
					"date", folderName,
					"error", err)
			} else {
				c.logger.Infow("Successfully cleaned image folder",
					"folderPath", folderPath,
					"date", folderName)
			}
		}),
	)
}

func (c *Client) LoadConfTask() (gocron.Job, error) {
	return c.scheduler.NewJob(
		gocron.DurationJob(time.Hour*2),
		gocron.NewTask(func() {
			ctx, cancel := context.WithTimeout(context.Background(), time.Second*30)
			defer cancel()
			_ = c.ServerConf.LoadConf(ctx)
		}),
	)
}

func (c *Client) SyncLogTask() (gocron.Job, error) {
	return c.scheduler.NewJob(
		gocron.DurationJob(time.Minute*2),
		gocron.NewTask(func() {
			_ = c.LogDBSync.Sync()
		}),
	)
}
