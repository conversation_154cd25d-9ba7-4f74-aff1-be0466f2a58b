package cmd

import (
	"context"
	"fmt"
	"log/slog"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/sourcegraph/conc"
)

var StartPrint = `
Server Started at %s
`

type App struct {
	Options
}

func NewApp(opt Options) *App {
	app := &App{opt}
	//app.setDefaultSlog()
	return app
}

//func (a *App) setDefaultSlog() {
//	var extraWriters []xslog.ExtraWriter
//
//	if a.Config.IsDebugMode() {
//		a.Config.Log.Level = slog.LevelDebug
//		extraWriters = append(extraWriters, xslog.ExtraWriter{
//			Writer: os.Stdout,
//			Level:  slog.LevelDebug,
//		})
//	}
//
//	a.Config.Log.ExtraWriters = extraWriters
//	fileLogger := xslog.NewFileSlog(&a.Config.Log)
//	slog.SetDefault(fileLogger)
//}

func (a *App) Run() error {
	// start http server
	httpSrv, err := a.Http.Run()
	if err != nil {
		return err
	}
	// start crontab
	if err := a.Cron.StartAsync(); err != nil {
		return err
	}

	fmt.Printf(StartPrint, a.Http.Conf.App.Addr)

	// 监控结束指令
	quit := make(chan os.Signal)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	// 停止服务
	ctx, cancel := context.WithTimeout(context.Background(), time.Minute*5)
	defer cancel()

	var wg conc.WaitGroup
	wg.Go(func() {
		_ = a.LogDBSync.Sync()
	})
	wg.Go(a.Cron.Stop)
	wg.Go(func() {
		if err := httpSrv.Shutdown(ctx); err != nil {
			slog.Error("Server Shutdown", "err", err)
		}
	})
	if r := wg.WaitAndRecover(); r != nil {
		slog.Error("Server Shutdown", "wait err", r.String())
	}

	slog.Info("server exiting")
	return nil
}
