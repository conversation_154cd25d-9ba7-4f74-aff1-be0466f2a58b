package service

import (
	"context"
	"encoding/json"
	"imagezero/configs"
	"imagezero/internal/clients/pay"
	"imagezero/internal/clients/serverconf"
	"imagezero/internal/data"
	"imagezero/internal/data/ent"
	userSchema "imagezero/internal/data/ent/user"
	"imagezero/internal/data/schema"
	"imagezero/internal/dto/request"
	"imagezero/internal/dto/response"
	"imagezero/pkg/ecode"
	"imagezero/pkg/iap"
	"slices"
	"strconv"
	"time"

	"github.com/go-pay/gopay/apple"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

type ApplePayService struct {
	log             *zap.SugaredLogger
	rds             *redis.Client
	conf            *configs.Config
	serverConf      *serverconf.ServerConf
	paymentRepo     *data.PaymentRepo
	paymentPlanRepo *data.PaymentPlanRepo
	paymentLogsRepo *data.PaymentLogsRepo
	paymentService  *PaymentService
	opt             *Options
}

func NewApplePayService(opt *Options, log *zap.SugaredLogger, rds *redis.Client, conf *configs.Config, paymentRepo *data.PaymentRepo, paymentPlanRepo *data.PaymentPlanRepo, paymentLogsRepo *data.PaymentLogsRepo, paymentService *PaymentService) *ApplePayService {
	return &ApplePayService{
		opt:             opt,
		log:             log,
		rds:             rds,
		conf:            conf,
		serverConf:      opt.ServerConf,
		paymentRepo:     paymentRepo,
		paymentPlanRepo: paymentPlanRepo,
		paymentLogsRepo: paymentLogsRepo,
		paymentService:  paymentService,
	}
}

func (s *ApplePayService) PaymentVerifyV2(ctx context.Context, user *ent.User, ipInfo *request.IPInfo, req *request.PaymentVerifyReqV2) (*response.PaymentInfoResp, error) {
	userID := user.ID
	isRecover := req.IsRecover
	reqEnvironment := req.Environment
	transactionID := req.TransactionID
	originalTransactionID := req.OriginalTransactionID
	if transactionID == "" || originalTransactionID == "" {
		return nil, ecode.InvalidParams
	}
	s.log.Infoln("[PaymentVerifyV2]begin.", req.AppName, req.AppVersion, req.PromotionName, req.ProductID, userID, originalTransactionID, transactionID)

	clientType := req.ClientInfo.GetClientType()
	isIOS := clientType.IsiOS()
	isLive := false

	if slices.Contains(pay.BoosterPackProductIDList, req.ProductID) {
		_, transactionInfo, err := pay.GetTransactionInfo(s.conf.Payment.ApplePrivateKey, transactionID, isIOS, isLive)

		if err != nil {
			s.log.Warnln("[PaymentVerifyV2]Booster Pack, GetTransactionInfo failed.", userID, originalTransactionID, transactionID, err)
			s.paymentLogsRepo.CreateLog(ctx, schema.InvalidReceiptData, uint(userID), &originalTransactionID, map[string]interface{}{
				"transactionID":         transactionID,
				"originalTransactionID": originalTransactionID,
				"reqEnvironment":        reqEnvironment,
				"isLive":                isLive,
				"isIOS":                 isIOS,
				"err":                   err,
			})
			return nil, ecode.PaymentFailed
		}

		if transactionInfo.RevocationDate > 0 {
			s.log.Warnln("[PaymentVerifyV2]Booster Pack, invalid payment status.", userID, originalTransactionID, transactionID)
			s.paymentLogsRepo.CreateLog(ctx, schema.InvalidReceiptData, uint(userID), &originalTransactionID, map[string]interface{}{
				"transactionID":         transactionID,
				"originalTransactionID": originalTransactionID,
				"reqEnvironment":        reqEnvironment,
				"isLive":                isLive,
				"isIOS":                 isIOS,
				"err":                   "refunded",
			})
			return nil, ecode.InvalidPaymentStatus
		}

		// ONE_TIME_CHARGE
		productID := transactionInfo.ProductId
		quantity := transactionInfo.Quantity
		s.log.Infoln("[PaymentVerifyV2]GetTransactionInfo.", userID, productID, quantity, originalTransactionID, transactionID)
		if slices.Contains(pay.BoosterPackProductIDList, productID) {
			/*
				_, err := s.userQuotaPayment.PaymentCreateFromMobile(ctx, user, ipInfo, transactionID, pay.IOSBoosterPackProductID, quantity, "")
				s.log.Infoln("[PaymentVerifyV2]Booster Pack.", userID, productID, quantity, originalTransactionID, transactionID, err)
				info := s.paymentService.GetUserPaymentInfo(ctx, user.ID, false)
				if req != nil && s.paymentService.IsNewIOSVersion(req.AppName, req.AppVersion) {
					s.paymentService.FormatPaymentInfoResp(info)
				}
				return info, nil
			*/
		} else {
			s.log.Warnln("[PaymentVerifyV2]GetTransactionInfo error, invalid productID .", userID, req.ProductID, productID, quantity, originalTransactionID, transactionID)
			s.paymentLogsRepo.CreateLog(ctx, schema.InvalidProductID, uint(userID), &originalTransactionID, map[string]interface{}{
				"productID":                productID,
				"transactionID":            transactionID,
				"originalTransactionID":    originalTransactionID,
				"reqOriginalTransactionID": req.OriginalTransactionID,
				"reqEnvironment":           reqEnvironment,
				"isLive":                   isLive,
				"isIOS":                    isIOS,
				"err":                      "Invalid product ID",
			})
			info := s.paymentService.GetUserPaymentInfo(ctx, user.ID, false)
			//if req != nil && s.paymentService.IsNewIOSVersion(req.AppName, req.AppVersion) {
			//	s.paymentService.FormatPaymentInfoResp(info)
			//}
			return info, nil
		}
	}
	subscriptionInfo, transaction, renewalInfo, err := pay.GetAppleSubscriptionInfo(s.conf.Payment.ApplePrivateKey, originalTransactionID, isIOS, isLive)
	s.log.Infoln("[PaymentVerifyV2]GetAppleSubscriptionInfo.", userID, originalTransactionID, transactionID, subscriptionInfo, err)
	s.log.Infoln("[PaymentVerifyV2]GetAppleSubscriptionInfo.", userID, originalTransactionID, transactionID, transaction, err)
	s.log.Infoln("[PaymentVerifyV2]GetAppleSubscriptionInfo.", userID, originalTransactionID, transactionID, renewalInfo, err)

	reqOriginalTransactionID := req.OriginalTransactionID
	if transaction != nil {
		originalTransactionID = transaction.OriginalTransactionId
	}

	saveInvalidReceiptLog := func(errMsg string) {
		if errMsg == "" && err != nil {
			errMsg = err.Error()
		}
		s.paymentLogsRepo.CreateLog(ctx, schema.InvalidReceiptData, uint(userID), &originalTransactionID, map[string]interface{}{
			"transactionID":            transactionID,
			"originalTransactionID":    originalTransactionID,
			"reqOriginalTransactionID": reqOriginalTransactionID,
			"subscriptionInfo":         subscriptionInfo,
			"transaction":              transaction,
			"renewalInfo":              renewalInfo,
			"isRecover":                isRecover,
			"reqEnvironment":           reqEnvironment,
			"isLive":                   isLive,
			"isIOS":                    isIOS,
			"errMsg":                   errMsg,
			"err":                      err,
		})
	}

	if err != nil {
		saveInvalidReceiptLog("")
		return nil, ecode.VerifyFailed
	}
	if subscriptionInfo == nil || transaction == nil || renewalInfo == nil {
		saveInvalidReceiptLog("Invalid subscription")
		return nil, ecode.VerifyFailed
	}

	//defer s.paymentService.DeleteFindByUserIDCache(ctx, userID)
	//productID := renewalInfo.ProductId
	productID := renewalInfo.AutoRenewProductId
	quantity := int(transaction.Quantity)

	isCancelled := false
	isExpired := false
	isRefunded := false
	transactionID = transaction.TransactionId
	subscriptionInfoStatus := subscriptionInfo.Data[0].LastTransactions[0].Status
	if renewalInfo.AutoRenewStatus == 0 {
		isCancelled = true
	}

	timestamp := time.Now().Unix()
	expiresDate := transaction.ExpiresDate / 1000
	if subscriptionInfoStatus == 2 || (expiresDate > 0 && expiresDate <= timestamp) {
		isExpired = true
	}
	if subscriptionInfoStatus == 5 {
		isRefunded = true
	}

	savePaymentLog := func(errorMsg string) {
		s.paymentLogsRepo.CreateLog(ctx, schema.ReceiptVerify, uint(userID), &originalTransactionID, map[string]interface{}{
			"originalTransactionID":    originalTransactionID,
			"reqOriginalTransactionID": reqOriginalTransactionID,
			"isCancelled":              isCancelled,
			"isExpired":                isExpired,
			"isRefunded":               isRefunded,
			"cancellationDateMS":       transaction.RevocationDate,
			"cancellationReason":       transaction.RevocationReason,
			"expiresDateMS":            transaction.ExpiresDate,
			"productID":                productID,
			"quantity":                 quantity,
			"appAppleId":               subscriptionInfo.AppAppleId,
			"bundleId":                 subscriptionInfo.BundleId,
			"reqEnvironment":           reqEnvironment,
			"isLive":                   isLive,
			"status":                   subscriptionInfoStatus,
			"subscriptionInfo":         subscriptionInfo,
			"transaction":              transaction,
			"renewalInfo":              renewalInfo,
			"isRecover":                isRecover,
			"error":                    errorMsg,
		})
	}

	if isExpired || isRefunded {
		savePaymentLog("Invalid receipt status.")
		s.log.Warnln("[PaymentVerifyV2]isCancelled|isExpired|isRefunded", userID, reqOriginalTransactionID, originalTransactionID, isCancelled, isExpired, isRefunded)
		return nil, ecode.InvalidPaymentStatus
	}

	planInfo, _ := s.paymentPlanRepo.GetPlanByProductID(ctx, productID)
	if planInfo == nil || planInfo.ID <= 0 {
		savePaymentLog("Invalid product ID.")
		s.log.Warnln("[PaymentVerifyV2]Invalid product.", userID, productID, reqOriginalTransactionID, originalTransactionID)
		return nil, ecode.InvalidProduct
	}

	if req.ProductID != "" && req.ProductID != productID {
		s.log.Warnln("[PaymentVerifyV2]Product ID doesn't match.", userID, req.ProductID, productID, reqOriginalTransactionID, originalTransactionID)
		//savePaymentLog("Product ID doesn't match." + req.ProductID + "!=" + productID)
		//return nil, ecode.ProductIDNotMatch
	}

	if transaction.SubscriptionGroupIdentifier != pay.IOSPlanGroupID && transaction.SubscriptionGroupIdentifier != pay.MacPlanGroupID {
		savePaymentLog("Invalid group ID.")
		s.log.Warnln("[PaymentVerifyV2]Invalid product group.", userID, reqOriginalTransactionID, originalTransactionID, transaction)
		return nil, ecode.InvalidProduct
	}

	updated := false
	linkPlan := false
	plan, _ := s.paymentRepo.FindBySubID(ctx, originalTransactionID)
	if plan != nil && plan.ID > 0 {
		//update first
		oldPlanExpired := (plan.PayStatus == uint8(schema.PayStatusExpired))
		if oldPlanExpired {
			oldPlanCreateTime := uint64(time.Now().Unix())
			s.paymentRepo.UpdatePlan(ctx, planInfo.ID, originalTransactionID, expiresDate, isCancelled, oldPlanCreateTime)

			if int(plan.UserID) != userID {
				//fix, 2024.02.28
				currentPlan, _ := s.paymentService.GetPaymentInfo(ctx, user, nil)
				if currentPlan == nil || currentPlan.PlanName == string(PremiumLevelFree) {
					_, err = s.paymentRepo.TransferPlan(ctx, originalTransactionID, userID)
					s.paymentLogsRepo.CreateLog(ctx, schema.TransferPlan, uint(userID), &originalTransactionID, map[string]interface{}{
						"originalTransactionID":    originalTransactionID,
						"reqOriginalTransactionID": reqOriginalTransactionID,
						"reqEnvironment":           reqEnvironment,
						"isLive":                   isLive,
						"plan":                     plan,
						"error":                    err,
					})
					s.log.Infoln("[PaymentVerifyV2]Transfer Plan. current user:", userID, originalTransactionID, plan.UserID)
				}
			}

			//new plan
			plan, _ = s.paymentRepo.FindBySubID(ctx, originalTransactionID)
			s.AfterResubscribe(ctx, transactionID, productID, plan, planInfo, "")
		} else {
			oldPlanCreateTime := plan.CreateTime
			s.paymentRepo.UpdatePlan(ctx, planInfo.ID, originalTransactionID, expiresDate, isCancelled, oldPlanCreateTime)

			if int(plan.UserID) != userID {
				if user.RegisterType != userSchema.RegisterTypeDevice {
					return nil, ecode.NeedLoginAccount
				}

				userIDstr := strconv.Itoa(int(plan.UserID))
				logCount, _ := s.paymentLogsRepo.CountByObjID(ctx, schema.LinkPlan, userIDstr)
				transferCount, _ := s.paymentLogsRepo.CountByObjID(ctx, schema.LoginTransferPlan, originalTransactionID)
				if /*logCount >= 1 ||*/ transferCount > 0 {
					s.log.Warnln("[PaymentVerifyV2]Invalid device. current user:", userID, reqOriginalTransactionID, originalTransactionID, transferCount, plan.UserID)
					s.paymentLogsRepo.CreateLog(ctx, schema.InvalidDevice, uint(userID), &originalTransactionID, map[string]interface{}{
						"plan":                     plan,
						"logCount":                 logCount,
						"transferCount":            transferCount,
						"reqEnvironment":           reqEnvironment,
						"isLive":                   isLive,
						"originalTransactionID":    originalTransactionID,
						"reqOriginalTransactionID": reqOriginalTransactionID,
					})

					if transferCount > 0 {
						return nil, ecode.NeedLoginAccount
					} else {
						return nil, ecode.InvalidDevice
					}
				} else {
					log, _ := s.paymentLogsRepo.FindByUserIDAndObjID(ctx, schema.LinkPlan, uint(userID), userIDstr)
					if log != nil {
						s.log.Warnln("[PaymentVerifyV2]Already linked. current user:", userID, reqOriginalTransactionID, originalTransactionID, plan.UserID)
					} else {
						s.paymentLogsRepo.CreateLog(ctx, schema.LinkPlan, uint(userID), &userIDstr, map[string]interface{}{
							"subID":                    originalTransactionID,
							"reqOriginalTransactionID": reqOriginalTransactionID,
							"reqEnvironment":           reqEnvironment,
							"isLive":                   isLive,
							"plan":                     plan,
						})
						linkPlan = true
						s.log.Infoln("[PaymentVerifyV2]Link Plan. current user:", userID, reqOriginalTransactionID, originalTransactionID, plan.UserID)
					}
				}
			}
		}

		updated = true
		s.log.Warnln("[PaymentVerifyV2]Existing plan.", userID, plan, productID, reqOriginalTransactionID, originalTransactionID, isCancelled, isExpired, isRefunded)
	}

	isInitialBuy := transaction.TransactionId == transaction.OriginalTransactionId
	if plan == nil {
		currentPlan, _ := s.paymentService.GetPaymentInfo(ctx, user, &request.PaymentInfoReq{
			AppName:    req.AppName,
			AppVersion: req.AppVersion,
		})
		if currentPlan != nil && currentPlan.PlanName != string(PremiumLevelFree) {
			s.paymentLogsRepo.CreateLog(ctx, schema.AlreadyPaid, uint(userID), &originalTransactionID, map[string]interface{}{
				"currentPlan":              currentPlan,
				"reqEnvironment":           reqEnvironment,
				"isLive":                   isLive,
				"originalTransactionID":    originalTransactionID,
				"reqOriginalTransactionID": reqOriginalTransactionID,
			})

			s.log.Warnln("[PaymentVerifyV2]Already paid:", userID, reqOriginalTransactionID, originalTransactionID, currentPlan)

			//fix 2024.10.17, maybe upgrade
			if currentPlan.PaymentMethod == string(pay.PaymentPlatformApple) && currentPlan.SubID != originalTransactionID {
				oldPlanCreateTime := currentPlan.CreateTime
				_, err = s.paymentRepo.SetNewPlan(ctx, planInfo.ID, currentPlan.SubID, originalTransactionID, expiresDate, isCancelled, uint64(oldPlanCreateTime))
				if err == nil {
					plan, _ = s.paymentRepo.FindBySubID(ctx, originalTransactionID)
					s.AfterUpgrade(ctx, productID, transactionID, plan, planInfo)
				}
				s.paymentLogsRepo.CreateLog(ctx, schema.SetNewPlan, uint(userID), &originalTransactionID, map[string]interface{}{
					"currentPlan":              currentPlan,
					"OldSubID":                 currentPlan.SubID,
					"originalTransactionID":    originalTransactionID,
					"reqOriginalTransactionID": reqOriginalTransactionID,
					"transactionID":            transactionID,
					"isUpgraded":               transaction.IsUpgraded,
					"productID":                productID,
					"quantity":                 quantity,
					"appAppleId":               subscriptionInfo.AppAppleId,
					"bundleId":                 subscriptionInfo.BundleId,
					"reqEnvironment":           reqEnvironment,
					"isLive":                   isLive,
					"status":                   subscriptionInfoStatus,
					"subscriptionInfo":         subscriptionInfo,
					"transaction":              transaction,
					"renewalInfo":              renewalInfo,
					"error":                    err,
				})

				if plan != nil {
					info := s.paymentService.GetPaymentInfoResp(ctx, plan, planInfo, false)
					//if req != nil && s.paymentService.IsNewIOSVersion(req.AppName, req.AppVersion) {
					//	s.paymentService.FormatPaymentInfoResp(info)
					//}

					return info, nil
				}
			}
			return currentPlan, nil
		}

		now := time.Now().Unix()
		paymentExtra := &schema.PaymentExtra{
			IsSubscription: true,
			Trigger:        s.GetTrigger(productID),
			Env:            subscriptionInfo.Environment,
			ProductID:      productID,
		}
		if slices.Contains(pay.NewIOSProductIDList, productID) {
			paymentExtra.Price = planInfo.Price
			paymentExtra.Currency = planInfo.Currency
		}

		if ipInfo != nil {
			paymentExtra.IP = ipInfo.IP
			paymentExtra.Region = ipInfo.CountryShort
		}
		paymentExtraData, _ := json.Marshal(paymentExtra)
		data := &ent.Payment{
			UserID:        uint(user.ID),
			PlanID:        uint(planInfo.ID),
			PaymentMethod: uint8(schema.PaymentMethodApple),
			SubID:         originalTransactionID,
			Status:        uint8(schema.SubscriptionStatusDefault),
			PayStatus:     uint8(schema.PayStatusPaid),
			DueTime:       uint64(expiresDate),
			CreateTime:    uint64(now),
			UpdateTime:    uint64(now),
			Extra:         string(paymentExtraData),
		}

		plan, err = s.paymentRepo.Create(ctx, data)
		if err != nil {
			savePaymentLog(err.Error())
			return nil, ecode.VerifyFailed
		}

		//send email
		s.afterSubscribed(ctx, userID, plan)
	}

	s.log.Infoln("[PaymentVerifyV2]Done.", userID, plan, productID, reqOriginalTransactionID, originalTransactionID, isCancelled, isExpired, isRefunded, isInitialBuy)

	if plan == nil || plan.ID == 0 {
		savePaymentLog(ecode.PaymentFailed.Error())
		return nil, ecode.PaymentFailed
	}

	savePaymentLog("")
	if !linkPlan {
		//s.SaveUserOfferDiscount(ctx, userID, req.PromotionName, transaction)
	}
	if updated {
		originPlanID := plan.PlanID
		plan, _ = s.paymentRepo.FindBySubID(ctx, originalTransactionID)
		if originPlanID != plan.PlanID {
			s.AfterUpgrade(ctx, productID, transactionID, plan, planInfo)
			s.log.Infoln("[PaymentVerifyV2]AfterUpgrade.", userID, originPlanID, productID, reqOriginalTransactionID, originalTransactionID)
		} else {
			s.log.Infoln("[PaymentVerifyV2]Updated.", userID, originPlanID, productID, reqOriginalTransactionID, originalTransactionID)
		}
	}

	info := s.paymentService.GetPaymentInfoResp(ctx, plan, planInfo, false)
	//if req != nil && s.paymentService.IsNewIOSVersion(req.AppName, req.AppVersion) {
	//	s.paymentService.FormatPaymentInfoResp(info)
	//}
	return info, nil
}

func (s *ApplePayService) GetTrigger(productID string) string {
	if slices.Contains(pay.AllIOSProductIDList, productID) {
		return "app_ios"
	}

	return "app_mac"
}

func (s *ApplePayService) checkIsInitialBuy(receipt *iap.IAPResponse) bool {
	if receipt.LatestReceiptInfo == nil {
		return false
	}
	return len(receipt.LatestReceiptInfo) == 1 &&
		receipt.LatestReceiptInfo[0].OriginalTransactionID == receipt.LatestReceiptInfo[0].TransactionID
}

func (s *ApplePayService) HandleNotification(ctx context.Context, requestBytes []byte) error {
	res := &apple.NotificationV2Req{}
	err := json.Unmarshal(requestBytes, res)
	if err != nil {
		s.log.Warnln("[HandleNotification]Invalid data.", err)
		return err
	}

	// decode signedPayload
	payload, err := apple.DecodeSignedPayload(res.SignedPayload)
	if err != nil {
		s.log.Warnln("[HandleNotification]Decode error:", err)
		return err
	}

	s.log.Infoln("[HandleNotification]payload.NotificationType:", payload.NotificationType, payload.Subtype)

	//decode renewalInfo
	renewalInfo, err := payload.DecodeRenewalInfo()
	if err != nil {
		s.log.Warnln("[HandleNotification]Decode renewalInfo.", err)
		return err
	}

	//decode transactionInfo
	transactionInfo, err := payload.DecodeTransactionInfo()
	if err != nil {
		s.log.Warnln("[HandleNotification]error:", err)
		return err
	}

	//boast pack
	/*
		productID := renewalInfo.AutoRenewProductId
		transactionID := transactionInfo.TransactionId

		if slices.Contains(pay.BoosterPackProductIDList, productID) && payload.NotificationType == iap.IAP_NOTIFICATION_REFUND {
			log, _ := s.opt.UserQuotaPaymentRepo.FindByChargeID(ctx, transactionInfo.TransactionId)
			if log != nil {
				quotaError := s.userQueryQuota.DeductBoosterPackQuotaByRefund(ctx, int(log.UserID), transactionID)
				s.log.Infoln("[HandleNotification]DeductBoosterPackQuotaByRefund.", log.UserID, transactionID, quotaError)
			} else {
				s.log.Warnln("[HandleNotification]FindByChargeID failed.", productID, transactionID)
			}
			return nil
		}
	*/
	originalTransactionID := renewalInfo.OriginalTransactionId
	plan, _ := s.paymentService.paymentRepo.FindBySubID(ctx, originalTransactionID)
	if plan == nil || plan.ID <= 0 {
		s.log.Warnln("[HandleNotification]Invalid plan:", originalTransactionID, payload.NotificationType, payload.Subtype)
		return nil
	}

	userID := plan.UserID
	isCancelled := false
	isExpired := false
	isRefunded := false
	if renewalInfo.AutoRenewStatus == iap.IAP_SUBSCRIPTION_AUTO_RENEW_OFF {
		isCancelled = true
	}

	timestamp := time.Now().Unix()
	expiresDate := transactionInfo.ExpiresDate / 1000
	if expiresDate <= timestamp {
		isExpired = true
	}

	cancellationDate := transactionInfo.RevocationDate / 1000
	if cancellationDate > int64(0) {
		isRefunded = true
	}

	s.log.Infoln("[HandleNotification]isCancelled|isExpired|isRefunded", userID, originalTransactionID, isCancelled, isExpired, isRefunded)
	err = s.DoHandleNotification(ctx, plan, isCancelled, isExpired, isRefunded, payload, renewalInfo, transactionInfo)
	payload.Data.SignedRenewalInfo = ""
	payload.Data.SignedTransactionInfo = ""
	notificationType := payload.NotificationType
	notificationSubType := payload.Subtype
	s.paymentLogsRepo.CreateLog(ctx, schema.HandleNotification, uint(userID), &originalTransactionID, map[string]interface{}{
		"data":                  string(requestBytes),
		"payload":               payload,
		"notificationType":      notificationType,
		"notificationSubType":   notificationSubType,
		"isCancelled":           isCancelled,
		"isExpired":             isExpired,
		"isRefunded":            isRefunded,
		"cancellationDateMS":    transactionInfo.RevocationDate,
		"cancellationReason":    transactionInfo.RevocationReason,
		"expiresDateMS":         transactionInfo.ExpiresDate,
		"renewalInfo":           renewalInfo,
		"transactionInfo":       transactionInfo,
		"planID":                plan.ID,
		"planStatus":            plan.Status,
		"originalTransactionID": originalTransactionID,
		"error":                 err,
	})

	//s.paymentService.DeleteFindByUserIDCache(ctx, int(plan.UserID))
	s.log.Infoln("[HandleNotification]Done.", userID, originalTransactionID, notificationType, notificationSubType, err)

	return nil
}

func (s *ApplePayService) DoHandleNotification(ctx context.Context, plan *ent.Payment, isCancelled bool, isExpired bool, isRefunded bool, payload *apple.NotificationV2Payload, renewalInfo *apple.RenewalInfo, transactionInfo *apple.TransactionInfo) error {
	subID := renewalInfo.OriginalTransactionId
	notificationType := payload.NotificationType
	notificationSubType := payload.Subtype

	var doUpdate = func(isRenew bool) error {
		productID := renewalInfo.AutoRenewProductId
		planInfo, _ := s.paymentPlanRepo.GetPlanByProductID(ctx, productID)
		if planInfo == nil || planInfo.ID <= 0 {
			s.log.Warnln("[DoHandleNotification]Invalid product.", plan.UserID, productID)
			return ecode.InvalidProduct
		}

		expiredTime := transactionInfo.ExpiresDate / 1000
		serverProductID := s.paymentPlanRepo.GetProductIDByPlanID(ctx, int(plan.PlanID))
		if isRenew {
			s.paymentRepo.PayRenew(ctx, subID, expiredTime)
			//reward
			//awardID := plan.SubID + "_" + transactionInfo.TransactionId
			//s.paymentService.AwardGPT4ByPremium(ctx, int(plan.UserID), awardID, int(plan.PlanID))
		} else {
			if productID != serverProductID {
				s.paymentRepo.PayUpgrade(ctx, subID, planInfo.ID, expiredTime)
				//s.SaveUserOfferDiscount(ctx, int(plan.UserID), "", transactionInfo)
				s.AfterUpgrade(ctx, productID, transactionInfo.TransactionId, plan, planInfo)
			}
		}
		s.log.Infoln("[DoHandleNotification]doUpdate.", plan.UserID, subID, serverProductID, productID)
		return nil
	}

	expiredTime := transactionInfo.ExpiresDate / 1000
	if notificationType == iap.IAP_NOTIFICATION_DID_CHANGE_RENEWAL_PREF {
		if notificationSubType == iap.IAP_NOTIFICATION_SUB_UPGRADE {
			return doUpdate(false)
		} else if notificationSubType == iap.IAP_NOTIFICATION_SUB_DOWNGRADE {
			return doUpdate(false)
		} else if notificationSubType == iap.IAP_NOTIFICATION_SUB_DEFAULT {
			return doUpdate(false)
		}
	} else if notificationType == iap.IAP_NOTIFICATION_DID_CHANGE_RENEWAL_STATUS {
		if notificationSubType == iap.IAP_NOTIFICATION_SUB_AUTO_RENEW_ENABLED {
			s.paymentRepo.PayResume(ctx, subID)
		} else if notificationSubType == iap.IAP_NOTIFICATION_SUB_AUTO_RENEW_DISABLED {
			s.paymentRepo.PayCancel(ctx, subID)
		}
	} else if notificationType == iap.IAP_NOTIFICATION_DID_RENEW {
		return doUpdate(true)
	} else if notificationType == iap.IAP_NOTIFICATION_DID_FAIL_TO_RENEW {
		s.paymentRepo.PayExpired(ctx, subID)
	} else if notificationType == iap.IAP_NOTIFICATION_EXPIRED {
		if notificationSubType == iap.IAP_NOTIFICATION_SUB_VOLUNTARY {
			s.paymentRepo.PayExpired(ctx, subID)
		} else if notificationSubType == iap.IAP_NOTIFICATION_SUB_BILLING_RETRY {
			s.paymentRepo.PayExpired(ctx, subID)
		} else if notificationSubType == iap.IAP_NOTIFICATION_SUB_PRICE_INCREASE {
			//Nothing to do.
		}
		s.log.Warnln("[DoHandleNotification]Expired.", plan.UserID, subID)
	} else if notificationType == iap.IAP_NOTIFICATION_REFUND {
		s.paymentRepo.PayExpired(ctx, subID)
		/*
			awardID := plan.SubID + "_" + transactionInfo.TransactionId

			if slices.Contains(YearlyPocketPlan, int(plan.PlanID)) {
				quotaError := s.userQueryQuota.DeductQuotaByRefund(ctx, int(plan.UserID), awardID)
				s.log.Infoln("[HandleRefundWebHook]DeductQuotaByRefund.", plan.UserID, plan.PlanID, subID, quotaError)
			}
		*/
	} else if notificationType == iap.IAP_NOTIFICATION_SUBSCRIBED {
		if notificationSubType == iap.IAP_NOTIFICATION_SUB_INITIAL_BUY {
			//Nothing to do.
		} else if notificationSubType == iap.IAP_NOTIFICATION_SUB_RESUBSCRIBE {
			time.Sleep(4 * time.Second)
			productID := renewalInfo.ProductId

			planInfo, _ := s.paymentPlanRepo.GetPlanByProductID(ctx, productID)
			if planInfo == nil || planInfo.ID <= 0 {
				s.log.Warnln("[DoHandleNotification]Invalid product.", plan.UserID, productID, subID)
				return ecode.InvalidProduct
			}
			planID := planInfo.ID
			p, _ := s.paymentRepo.FindByUserID(ctx, uint(plan.UserID))
			if p != nil && p.PayStatus != uint8(schema.PayStatusExpired) {
				s.log.Warnln("[DoHandleNotification]Already paid", plan.UserID, productID, subID)
				return ecode.AlreadyPaid
			}

			s.paymentRepo.PayResubscribe(ctx, subID, planID, expiredTime)
			plan, _ = s.paymentService.paymentRepo.FindBySubID(ctx, subID)
			s.log.Infoln("[DoHandleNotification]resubscribe.", plan.UserID, planID, plan.PlanID, subID)
			//s.SaveUserOfferDiscount(ctx, int(plan.UserID), "", transactionInfo)
			s.AfterResubscribe(ctx, transactionInfo.TransactionId, productID, plan, planInfo, iap.IAP_NOTIFICATION_SUB_RESUBSCRIBE)
		}
	}
	return nil
}

func (s *ApplePayService) afterSubscribed(ctx context.Context, userID int, plan *ent.Payment) {
	/*
		if slices.Contains(YearlyPocketPlan, int(plan.PlanID)) {
			quotaError := s.userQueryQuota.AwardQuotaByMobilePremium(ctx, userID, plan.SubID)
			s.log.Infoln("[afterSubscribed]AwardQuotaByMobilePremium.", userID, plan.PlanID, plan.SubID, quotaError)
		}
	*/
}

func (s *ApplePayService) AfterResubscribe(ctx context.Context, transactionId string, productID string, plan *ent.Payment, planInfo *configs.PaymentPlan, from string) error {
	var extra = schema.PaymentExtra{}
	if plan.Extra != "" {
		json.Unmarshal([]byte(plan.Extra), &extra)
	}
	extra.ResubscribeTime = time.Now().Unix()
	if slices.Contains(pay.NewIOSProductIDList, productID) {
		extra.Price = planInfo.Price
	}
	extra.ProductID = productID
	extraData, _ := json.Marshal(&extra)
	extraStr := string(extraData)
	s.paymentRepo.UpdateExtra(ctx, plan.ID, extraStr)

	/*
		awardID := plan.SubID + "_" + transactionId
		if slices.Contains(YearlyPocketPlan, planInfo.ID) && from != iap.IAP_NOTIFICATION_SUB_RESUBSCRIBE {
			quotaError := s.userQueryQuota.AwardQuotaByMobilePremium(ctx, int(plan.UserID), awardID)
			s.log.Infoln("[AfterResubscribe]AwardQuotaByMobilePremium.", from, plan.UserID, plan.PlanID, planInfo.ID, awardID, quotaError)
		}
	*/
	s.log.Infoln("[AfterResubscribe]Update extra.", plan.UserID, planInfo.ID, plan.SubID, from)
	return nil
}

func (s *ApplePayService) AfterUpgrade(ctx context.Context, productID string, transactionId string, plan *ent.Payment, planInfo *configs.PaymentPlan) error {
	var extra = schema.PaymentExtra{}
	if plan.Extra != "" {
		json.Unmarshal([]byte(plan.Extra), &extra)
	}
	extra.UpgradeTime = time.Now().Unix()
	if slices.Contains(pay.NewIOSProductIDList, productID) {
		extra.Price = planInfo.Price
	}
	extra.ProductID = productID
	extraData, _ := json.Marshal(&extra)
	extraStr := string(extraData)
	s.paymentRepo.UpdateExtra(ctx, plan.ID, extraStr)
	/*
		awardID := plan.SubID
		if slices.Contains(YearlyPocketPlan, planInfo.ID) {
			quotaError := s.userQueryQuota.AwardQuotaByMobilePremium(ctx, int(plan.UserID), awardID)
			s.log.Infoln("[AfterUpgrade]AwardQuotaByMobilePremium.", plan.UserID, plan.PlanID, planInfo.ID, awardID, quotaError)
		}
	*/

	s.log.Infoln("[AfterUpgrade]Update extra.", plan.UserID, plan.SubID)
	return nil
}
