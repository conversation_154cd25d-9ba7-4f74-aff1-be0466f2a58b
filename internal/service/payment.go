package service

import (
	"context"
	"encoding/json"
	"fmt"
	"slices"
	"strconv"
	"strings"

	"imagezero/configs"
	"imagezero/internal/clients/pay"
	"imagezero/internal/clients/serverconf"
	"imagezero/internal/data"
	"imagezero/internal/data/ent"
	"imagezero/internal/data/ent/user"
	"imagezero/internal/data/schema"
	"imagezero/internal/dto/request"
	"imagezero/internal/dto/response"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

type PremiumLevel string

const (
	PremiumLevelFree    PremiumLevel = "Free"
	PremiumLevelStarter PremiumLevel = "Lite"
	PremiumLevelBasic   PremiumLevel = "Starter"
	PremiumLevelPro     PremiumLevel = "Professional"
	PremiumLevelUltra   PremiumLevel = "Ultra"  //Unlimited
	PremiumLevelPocket  PremiumLevel = "Pocket" //Pocket
)

func (s PremiumLevel) String() string {
	return string(s)
}

type PaymentInterval string

const (
	PaymentIntervalOnce       PaymentInterval = "once"
	PaymentIntervalWeek       PaymentInterval = "week"
	PaymentIntervalMonth      PaymentInterval = "month"
	PaymentIntervalThreeMonth PaymentInterval = "month3"
	PaymentIntervalSixMonth   PaymentInterval = "month6"
	PaymentIntervalYear       PaymentInterval = "year"
)

type PaymentService struct {
	log             *zap.SugaredLogger
	rds             *redis.Client
	conf            *configs.Config
	paymentRepo     *data.PaymentRepo
	paymentPlanRepo *data.PaymentPlanRepo
	paymentLogsRepo *data.PaymentLogsRepo
	serverConf      *serverconf.ServerConf
	opt             *Options
}

func NewPaymentService(opt *Options, log *zap.SugaredLogger, rds *redis.Client, conf *configs.Config, paymentRepo *data.PaymentRepo, paymentPlanRepo *data.PaymentPlanRepo, paymentLogsRepo *data.PaymentLogsRepo) *PaymentService {
	return &PaymentService{
		opt:             opt,
		log:             log,
		rds:             rds,
		conf:            conf,
		serverConf:      opt.ServerConf,
		paymentRepo:     paymentRepo,
		paymentPlanRepo: paymentPlanRepo,
		paymentLogsRepo: paymentLogsRepo,
	}
}

func (s *PaymentService) GetPaymentInfo(ctx context.Context, user *ent.User, req *request.PaymentInfoReq) (*response.PaymentInfoResp, error) {
	ret := s.GetUserPaymentInfo(ctx, user.ID, true)

	return ret, nil
}

func (s *PaymentService) IsFree(ctx context.Context, userID int) bool {
	paymentInfo := s.GetUserPaymentInfo(ctx, userID, false)
	return paymentInfo.PlanName == PremiumLevelFree.String()
}

func (s *PaymentService) IsPremium(ctx context.Context, userID int) bool {
	paymentInfo := s.GetUserPaymentInfo(ctx, userID, false)
	return paymentInfo.PlanName != PremiumLevelFree.String()
}

func (s *PaymentService) GetPaymentStartTime(ctx context.Context, userID int) (isPremium bool, startTime int64) {
	paymentInfo := s.GetUserPaymentInfo(ctx, userID, false)
	if paymentInfo.PlanName != PremiumLevelFree.String() {
		return true, paymentInfo.CreateTime
	} else {
		return false, 0
	}
}

func (s *PaymentService) GetPaymentInfoByUserID(ctx context.Context, userID int) (isPremium bool, paymentInfo *response.PaymentInfoResp) {
	paymentInfo = s.GetUserPaymentInfo(ctx, userID, false)
	if paymentInfo.PlanName != PremiumLevelFree.String() {
		return true, paymentInfo
	} else {
		return false, paymentInfo
	}
}

func (s *PaymentService) GetUserPaymentInfo(ctx context.Context, userID int, getCustomerLink bool) *response.PaymentInfoResp {
	res := new(response.PaymentInfoResp)
	res.PlanName = string(PremiumLevelFree)
	if userID <= 0 {
		return res
	}

	p, _ := s.paymentRepo.FindByUserID(ctx, uint(userID))
	if p == nil || p.ID == 0 {
		log, _ := s.paymentLogsRepo.FindByUserID(ctx, schema.LinkPlan, uint(userID))
		if log != nil && log.ObjID != "" {
			linkUserID, _ := strconv.Atoi(log.ObjID)
			p, _ = s.paymentRepo.FindByUserID(ctx, uint(linkUserID))
		}
	}

	if p == nil || p.ID == 0 {
		return res
	}

	res = s.GetPaymentInfoResp(ctx, p, nil, getCustomerLink)

	return res
}

func (s *PaymentService) GetPaymentInfoResp(ctx context.Context, p *ent.Payment, plan *configs.PaymentPlan, getCustomerLink bool) *response.PaymentInfoResp {
	res := new(response.PaymentInfoResp)
	res.PlanName = string(PremiumLevelFree)

	if p == nil {
		return res
	}

	res.SubID = p.SubID
	res.IsPaidBefore = (p.SubID != "")
	if plan == nil {
		plan, _ = s.paymentPlanRepo.GetPlanByPlanID(ctx, int(p.PlanID))
		if plan == nil {
			return res
		}
	}

	if p.PayStatus == uint8(schema.PayStatusExpired) {
		s.log.Infoln("[GetPaymentInfoResp]Expired plan.", user.ID, p.SubID)
		return res
	}

	res.PlanName = plan.DisplayName
	res.PlanID = plan.ID
	res.Interval = plan.Interval

	if p.PaymentMethod == uint8(schema.PaymentMethodStripe) {
		res.PaymentMethod = string(pay.PaymentPlatformStripe)
	} else if p.PaymentMethod == uint8(schema.PaymentMethodApple) {
		res.PaymentMethod = string(pay.PaymentPlatformApple)
	} else if p.PaymentMethod == uint8(schema.PaymentMethodPaypal) {
		res.PaymentMethod = string(pay.PaymentPlatformPaypal)
	} else if p.PaymentMethod == uint8(schema.PaymentMethodGoogle) {
		res.PaymentMethod = string(pay.PaymentPlatformGoogle)
	}
	res.CreateTime = int64(p.CreateTime)
	res.NextBillingTime = int64(p.DueTime)

	res.SubscriptionStatus = s.FormatStatus(schema.SubscriptionStatus(p.Status))
	res.IsTrial = p.PayStatus == uint8(schema.PayStatusTrial)
	res.PayStatus = s.FormatPayStatus(schema.PayStatus(p.PayStatus))
	res.SubID = p.SubID
	res.IsGift = strings.HasPrefix(p.SubID, "gift")
	res.PaidUserID = int(p.UserID)

	var extra = schema.PaymentExtra{}
	if p.Extra != "" {
		json.Unmarshal([]byte(p.Extra), &extra)
	}

	//for apple resubscribe
	if p.PaymentMethod == uint8(schema.PaymentMethodApple) {
		if extra.ResubscribeTime > 0 {
			res.CreateTime = extra.ResubscribeTime
		}

		if extra.ProductID != "" && slices.Contains(pay.AllIOSProductIDList, extra.ProductID) {
			res.IsIOS = true
		}
	}

	if p.PaymentMethod == uint8(schema.PaymentMethodStripe) {
		if extra.UpgradeTime > 0 {
			res.CreateTime = extra.UpgradeTime
		}
	}

	res.CustomerLink = ""

	return res
}

func (s *PaymentService) FormatStatus(status schema.SubscriptionStatus) string {
	switch status {
	case schema.SubscriptionStatusDefault:
		return "Normal"
	case schema.SubscriptionStatusCancelled:
		return "Cancelled"
	default:
		return ""
	}
}

func (s *PaymentService) FormatPayStatus(status schema.PayStatus) string {
	switch status {
	case schema.PayStatusDefault:
		return "Default"
	case schema.PayStatusPaid, schema.PayStatusTrial:
		return "Paid"
	case schema.PayStatusPayFailed:
		return "Pay failed"
	case schema.PayStatusExpired:
		return "Expired"
	default:
		return ""
	}
}

func (s *PaymentService) Test(ctx context.Context, req *request.HealthReq) (*response.PingResponse, error) {
	fmt.Println(s.conf.Payment.ApplePrivateKey)
	return nil, nil
}
