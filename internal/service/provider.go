package service

import (
	"github.com/Sider-ai/go-pkg/xoss"

	"imagezero/configs"
	"imagezero/internal/clients/serverconf"

	"github.com/google/wire"
	"github.com/redis/go-redis/v9"

	"imagezero/pkg/jwt"

	"go.uber.org/zap"

	"imagezero/internal/clients"
	"imagezero/internal/clients/hashid"
	"imagezero/internal/data"

	"imagezero/internal/clients/openai"
)

var ProviderSet = wire.NewSet(
	wire.Struct(new(Options), "*"),
	NewHealthSrv,
	NewUserService,
	NewPaymentService,
	NewApplePayService,
	NewImageSrv,
	NewUserBenefitService,
	NewXOSS,
)

type Options struct {
	Log             *zap.SugaredLogger
	HashID          *hashid.HashID
	ServerConf      *serverconf.ServerConf
	Jwt             *jwt.JWT
	Conf            *configs.Config
	UserRepo        *data.UserRepo
	UserRequestRepo *data.UserRequestRepo
	RDS             *clients.RDS
	Redis           *redis.Client
	Xoss            xoss.Oss

	PaymentRepo     *data.PaymentRepo
	PaymentLogsRepo *data.PaymentLogsRepo
	PaymentPlanRepo *data.PaymentPlanRepo

	OpenaiClient      *openai.Client
	StyleTemplateRepo *data.StyleTemplateRepo
}

func NewXOSS(config *configs.Config) xoss.Oss {
	return xoss.NewOss(&xoss.Config{
		Platform: config.OSS.Platform,
		Aws:      config.OSS.Aws,
	})
}
