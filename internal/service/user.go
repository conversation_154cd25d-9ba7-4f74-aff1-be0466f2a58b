package service

import (
	"context"
	"errors"
	"log/slog"
	"time"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"

	"web-coder-app/internal/clients"
	"web-coder-app/internal/clients/hashid"
	"web-coder-app/internal/data"
	"web-coder-app/internal/data/ent"
	userSchema "web-coder-app/internal/data/ent/user"
	"web-coder-app/internal/dto/request"
	"web-coder-app/internal/dto/response"
	"web-coder-app/pkg/ecode"
	"web-coder-app/pkg/jwt"
	"web-coder-app/pkg/xphone"
)

type UserService struct {
	userRepo *data.UserRepo
	rds      *clients.RDS
	redis    *redis.Client
	hashID   *hashid.HashID
	jwt      *jwt.JWT
}

func NewUserService(opt *Options) *UserService {
	return &UserService{
		rds:      opt.RDS,
		redis:    opt.Redis,
		userRepo: opt.UserRepo,
		hashID:   opt.HashID,
		jwt:      opt.Jwt,
	}
}

func (s *UserService) RegisterByDeviceID(ctx context.Context, req *request.UserDeviceReq,
	ipInfo *request.IPInfo) (*response.UserLoginInfoV2, error) {

	// Add check for empty DeviceID
	if req.DeviceID == "" {
		return nil, ecode.InvalidParams
	}

	//var (
	//	start = time.Now().AddDate(0, 0, -1)
	//	count = 20
	//	//extLimitVersion = version.Must(version.NewVersion("1.0.13"))
	//)

	// ios和mac没上登录，暂时不限制
	//v, _ := req.GetAppVersion()
	//isExt := req.GetClientType().IsExt()
	//isWeb := req.GetClientType().IsWeb()
	//isWin := req.GetClientType().IsWin()
	//isAndroid := req.GetClientType().IsAndroid()
	//if isExt || isWeb || isWin || isAndroid {
	//deviceLimit := s.serverConf.GetDeviceRegisterLimit()
	//start = time.Now().Add(-deviceLimit.GetDuration())
	//count = deviceLimit.Count
	//}

	//// 24小时内同一个IP只能注册一定次数
	//registered, err := s.userRepo.CountDeviceRegisterWithIP(ctx, ipInfo.IP, start)
	//if err != nil {
	//	return nil, err
	//}
	//if registered >= count {
	//	return nil, ecode.RegisterTooMany
	//}

	// 首先检查是否已存在同一 DeviceID 的用户
	existingUser, err := s.userRepo.FindByDeviceID(ctx, req.DeviceID)
	if err == nil {
		// 用户已存在，直接生成 token 并返回
		token, err := s.generateToken(existingUser)
		if err != nil {
			return nil, err
		}
		return &response.UserLoginInfoV2{
			User:  existingUser,
			Token: token,
		}, nil
	} else if !errors.Is(err, ecode.NotFound) && !ent.IsNotFound(err) {
		// 发生了除"未找到"以外的错误
		return nil, err
	}

	// 用户不存在，创建新用户
	u := &ent.User{
		DeviceID:           req.DeviceID,
		RegisterIP:         ipInfo.IP,
		RegisterRegion:     ipInfo.CountryShort,
		RegisterAppName:    req.GetAppName(),
		RegisterAppVersion: req.AppVersion,
		RegisterType:       userSchema.RegisterTypeDevice,
		Avatar:             "",
	}
	user, err := s.userRepo.Create(ctx, u)
	if err != nil {
		return nil, err
	}
	token, err := s.generateToken(user)
	if err != nil {
		return nil, err
	}
	return &response.UserLoginInfoV2{
		User:  user,
		Token: token,
	}, nil
}

func (s *UserService) generateToken(user *ent.User) (*response.Token, error) {
	claims := jwt.ClaimsParam{
		UserID:       user.ID,
		RegisterType: string(user.RegisterType),
		AppName:      string(user.RegisterAppName),
		TokenID:      uuid.NewString(),
	}
	token, err := s.jwt.GenerateToken(claims, time.Hour*24*360)
	if err != nil {
		return nil, err
	}
	//refreshToken, err := s.jwt.GenerateToken(claims, time.Hour*24*210)
	//if err != nil {
	//	return nil, err
	//}
	return &response.Token{
		Token:           token.Token,
		TokenID:         claims.TokenID,
		ExpireAt:        token.ExpireAt,
		RefreshToken:    "discard",
		RefreshExpireAt: token.ExpireAt + 3600,
	}, nil
}

func (s *UserService) getUserInfo(ctx context.Context, user *ent.User, token *response.Token) (*response.UserLoginInfo, error) {
	res := &response.UserLoginInfo{
		UserInfo: response.UserInfo{
			ID:              s.hashID.User.EncodeNotE(user.ID),
			UserID:          user.ID,
			Email:           user.Email,
			Avatar:          user.Avatar,
			RegisterType:    string(user.RegisterType),
			NickName:        user.Nickname,
			Phone:           xphone.HideSensitive(user.Phone),
			RegisterAt:      user.CreateTime.Unix(),
			RegisterRegion:  user.RegisterRegion,
			RegisterAppName: string(user.RegisterAppName),
		},
		Token:         token,
		TestPartition: "",
	}
	// 先判断有没有设置过
	hasSetMap := "user:test:partition:bitmap:has:set"
	val, err := s.redis.GetBit(ctx, hasSetMap, int64(user.ID)).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		slog.Error("service.getUserInfo:test_partition_setting", "error", err, "result", val)
		return res, nil
	} else if val == 1 {
		bitmapKey := "user:test:partition:bitmap:compact"
		isMarked := ""
		val, err = s.redis.GetBit(ctx, bitmapKey, int64(user.ID)).Result()
		if err != nil && !errors.Is(err, redis.Nil) {
			slog.Error("service.getUserInfo:test_partition_getting", "error", err, "result", val)
			return res, nil
		}
		switch val {
		case 0:
			isMarked = "B"
		case 1:
			isMarked = "A"
		}
		res.TestPartition = isMarked
	}
	return res, nil
}

//func (s *UserService) SignFileCloudFrontCookies(user *ent.User) (signedCookies *filecloudfront.SignedCookies, err error) {
//	var userHashID string
//	userHashID, err = s.hashID.User.Encode(user.ID)
//	if err != nil {
//		return nil, err
//	}
//	return s.fileCloudFrontClient.SignCookiesForUser(userHashID, 24*30*time.Hour)
//}
