package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"

	"imagezero/internal/clients/serverconf"
	"imagezero/internal/dto/response"
	"imagezero/pkg/ecode"
	"imagezero/pkg/xtime"
)

const (
	trialKeyPrefix        = "trial:count:%s"          // 试用计数键前缀
	maxTrialCount         = 30                        // 试用总额度
	trialUseAmount        = 10                        // 每次试用消耗额度
	trialTTL              = 10 * 365 * 24 * time.Hour // 试用键5年TTL
	shortOperationTimeout = 5 * time.Second           // Redis操作超时
	subscriptionKeyPrefix = "sub:count:%s"            // 订阅计数键前缀

	// 不同付费类型的配额设置
	weeklySubscriptionQuota  = 300  // 周付费总额度
	monthlySubscriptionQuota = 1200 // 月付费总额度
	yearlySubscriptionQuota  = 1200 // 年付费月度总额度
	subscriptionUseAmount    = 10   // 每次使用消耗额度

	userIntervalKeyPrefix = "user:interval:%d" // 用户订阅类型记录键前缀
)

var (
	ErrTrialLimitReached = ecode.FeatureUseUp
	ErrQuotaLimitReached = ecode.RateLimitExceeded
	ErrNoSubscription    = ecode.FeatureNeedPremiumUse
)

// UserBenefitService manages user benefit logic using Redis.
type UserBenefitService struct {
	opt        *Options
	paymentSrv *PaymentService
}

// NewUserBenefitService creates a new UserBenefitService.
func NewUserBenefitService(opt *Options, paymentSrv *PaymentService) *UserBenefitService {
	return &UserBenefitService{
		opt:        opt,
		paymentSrv: paymentSrv,
	}
}

// getExpireFromPeriod calculates the expiration duration based on the limit period.
func (u *UserBenefitService) getExpireFromPeriod(loc *time.Location, period serverconf.LimitPeriod) time.Duration {
	errFmt := "service.UserBenefitService.getExpireFromPeriod error"
	var d time.Duration
	switch period {
	case serverconf.LimitPeriodDaily:
		d = xtime.NowToEndOfDayDuration(loc)
		if d <= 0 {
			u.opt.Log.Errorw(errFmt, "duration", d, "loc", loc, "period", period)
			d = time.Hour * 24
		}
	case serverconf.LimitPeriodWeekly:
		d = xtime.NowToEndOfWeekDuration(loc)
		if d <= 0 {
			u.opt.Log.Errorw(errFmt, "duration", d, "loc", loc, "period", period)
			d = time.Hour * 24 * 7
		}
	case serverconf.LimitPeriodMonthly:
		d = xtime.NowToEndOfMonthDuration(loc) // Simplified for now
		if d <= 0 {
			u.opt.Log.Errorw(errFmt, "duration", d, "loc", loc, "period", period)
			d = time.Hour * 24 * 30 // Approximate fallback
		}
	case serverconf.LimitPeriodTotal:
		// Using a very long duration for "total" limit, e.g., 5 years.
		d = time.Hour * 24 * 365 * 5
	default:
		u.opt.Log.Errorw(errFmt, "unexpected period", period, zap.Stack("stack"))
		d = time.Hour * 24 // Default to 24 hours if period is unknown
	}
	return d
}

// RecordCountUse increments the count for the given key, typically after a successful operation.
// It ensures the operation only happens if the key has a TTL > 1s.
func (u *UserBenefitService) RecordCountUse(ctx context.Context, key string, n int64) error {
	if key == "" {
		u.opt.Log.Warnw("RecordCountUse: called with empty key")
		return nil // Or return an error? Depends on desired behavior.
	}

	// Use a shorter timeout for Redis operations within the service logic
	opCtx, cancel := context.WithTimeout(ctx, 2*time.Second)
	defer cancel()

	du, err := u.opt.Redis.TTL(opCtx, key).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			u.opt.Log.Warnw("RecordCountUse: key does not exist, cannot increment", "key", key)
			// Decide if this is an error or should be ignored.
			// If the key should exist, this might indicate a problem.
			return nil // Or return fmt.Errorf("key %s does not exist", key)
		}
		u.opt.Log.Errorw("RecordCountUse: failed to get TTL", "key", key, "error", err)
		return err
	}

	// Only increment if the key has a reasonable TTL remaining.
	if du.Seconds() >= 1 {
		if n <= 0 {
			n = 1 // Default increment is 1
		}
		err = u.opt.Redis.IncrBy(opCtx, key, n).Err()
		if err != nil {
			u.opt.Log.Errorw("RecordCountUse: failed to increment key", "key", key, "increment", n, "error", err)
		}
		return err
	} else {
		// Key exists but is about to expire or has no TTL (-1).
		// Optionally delete it, or log a warning. Deleting might hide issues.
		u.opt.Log.Warnw("RecordCountUse: key exists but has TTL < 1s or no TTL, not incrementing", "key", key, "ttl", du)
		// Consider if deleting is the right action: return u.opt.Redis.Del(opCtx, key).Err()
		return nil // Or an error indicating the state
	}
}

// RecordCountUseFail decrements the count for the given key, typically after a failed operation (rollback).
// It ensures the operation only happens if the key has a TTL > 1s.
// It also prevents the count from becoming negative, setting it to 0 instead.
func (u *UserBenefitService) RecordCountUseFail(ctx context.Context, key string, n int64) error {
	if key == "" {
		u.opt.Log.Warnw("RecordCountUseFail: called with empty key")
		return nil
	}
	// 确保 n 是正数，如果传入小于等于0，则默认为1
	if n <= 0 {
		n = 1 // Default decrement is 1
	}

	opCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	// 首先检查 TTL
	du, err := u.opt.Redis.TTL(opCtx, key).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			u.opt.Log.Warnw("RecordCountUseFail: key does not exist, cannot decrement", "key", key)
			return nil // 键不存在，无需操作
		}
		u.opt.Log.Errorw("RecordCountUseFail: failed to get TTL", "key", key, "error", err)
		return fmt.Errorf("failed to get TTL for key %s: %w", key, err) // 获取 TTL 失败，返回错误
	}

	// 只有当 TTL >= 1 秒时才继续
	if du.Seconds() >= 1 {
		// --- 开始修改 ---
		// 在执行 DecrBy 之前获取当前值，以防止结果为负
		currentVal, getErr := u.opt.Redis.Get(opCtx, key).Int64()

		if getErr != nil {
			// 处理 GET 操作可能出现的错误
			if errors.Is(getErr, redis.Nil) {
				// 如果 TTL >= 1s，这里出现 redis.Nil 是不符合预期的，可能表示竞态条件或不一致
				u.opt.Log.Warnw("RecordCountUseFail: key disappeared between TTL check and GET", "key", key)
				// 键已消失，无需操作
				return nil
			}
			// 其他 GET 错误，记录并返回
			u.opt.Log.Errorw("RecordCountUseFail: failed to get current value", "key", key, "error", getErr)
			return fmt.Errorf("failed to get value for key %s before decrement: %w", key, getErr)
		}

		// 检查减少 n 后是否会导致负数
		if currentVal < n {
			// 如果 currentVal 小于要减少的值 n，直接设置为 0
			u.opt.Log.Warnw("RecordCountUseFail: decrement would result in negative value, setting to 0 instead", "key", key, "current", currentVal, "decrement", n)
			// 使用 Set 命令将值设置为 0，并保持原有的 TTL (redis.KeepTTL)
			setErr := u.opt.Redis.Set(opCtx, key, 0, redis.KeepTTL).Err()
			if setErr != nil {
				u.opt.Log.Errorw("RecordCountUseFail: failed to set key to 0", "key", key, "error", setErr)
				return fmt.Errorf("failed to set key %s to 0: %w", key, setErr) // 设置为 0 失败，返回错误
			}
			return nil // 成功将值设为 0，防止了负数
		}

		// 如果 currentVal >= n，正常执行 DecrBy
		decrErr := u.opt.Redis.DecrBy(opCtx, key, n).Err()
		if decrErr != nil {
			u.opt.Log.Errorw("RecordCountUseFail: failed to decrement key", "key", key, "decrement", n, "error", decrErr)
			return fmt.Errorf("failed to decrement key %s: %w", key, decrErr) // DecrBy 失败，返回错误
		}
		// u.opt.Log.Debugw("RecordCountUseFail: successfully decremented key", "key", key, "decrement", n, "new_value", currentVal-n) // 可选：添加成功日志
		return nil // 递减成功
		// --- 结束修改 ---
	} else {
		// 如果 TTL < 1s 或没有 TTL (-1)，记录警告并且不执行任何操作
		u.opt.Log.Warnw("RecordCountUseFail: key exists but has TTL < 1s or no TTL, not decrementing", "key", key, "ttl", du)
		return nil
	}
}

// ConsumeTrial 尝试为给定用户ID使用试用额度
func (u *UserBenefitService) ConsumeTrial(ctx context.Context, userID string) error {
	if userID == "" {
		return errors.New("userID cannot be empty")
	}
	key := fmt.Sprintf(trialKeyPrefix, userID)

	opCtx, cancel := context.WithTimeout(ctx, shortOperationTimeout)
	defer cancel()

	// 1. 获取当前使用次数
	valStr, err := u.opt.Redis.Get(opCtx, key).Result()

	if errors.Is(err, redis.Nil) {
		// 键不存在，这是第一次使用
		u.opt.Log.Infow("ConsumeTrial: first trial attempt, creating key", "key", key, "userID", userID)
		opCtxSet, cancelSet := context.WithTimeout(ctx, shortOperationTimeout)
		defer cancelSet()
		// 设置初始值为trialUseAmount（第一次使用）
		setCmd := u.opt.Redis.SetNX(opCtxSet, key, trialUseAmount, trialTTL)
		if setErr := setCmd.Err(); setErr != nil {
			u.opt.Log.Errorw("ConsumeTrial: failed to setnx initial trial count", "key", key, "userID", userID, "error", setErr)
			return setErr
		}

		if !setCmd.Val() {
			// 极少发生的竞态：GET和SETNX之间键被创建
			u.opt.Log.Warnw("ConsumeTrial: race condition detected during initial set, retrying", "key", key, "userID", userID)
			opCtxRetryGet, cancelRetryGet := context.WithTimeout(ctx, shortOperationTimeout)
			defer cancelRetryGet()
			valStr, err = u.opt.Redis.Get(opCtxRetryGet, key).Result()
			if err != nil {
				u.opt.Log.Errorw("ConsumeTrial: failed to get value after SETNX race", "key", key, "userID", userID, "error", err)
				return fmt.Errorf("failed to get trial count after race condition for key %s: %w", key, err)
			}
			// 继续检查计数
		} else {
			// 成功设置初始值为trialUseAmount
			u.opt.Log.Infow("ConsumeTrial: successfully recorded first trial", "key", key, "userID", userID, "count", trialUseAmount)
			return nil // 成功记录第一次使用
		}
	} else if err != nil {
		// 其他Redis错误
		u.opt.Log.Errorw("ConsumeTrial: failed to get trial count", "key", key, "userID", userID, "error", err)
		return err
	}

	// 2. 检查使用次数是否达到上限
	count, parseErr := strconv.Atoi(valStr)
	if parseErr != nil {
		u.opt.Log.Errorw("ConsumeTrial: failed to parse trial count from redis", "key", key, "userID", userID, "value", valStr, "error", parseErr)
		return fmt.Errorf("invalid trial count value for key %s: %w", key, parseErr)
	}

	// 检查使用trialUseAmount后是否会超过上限
	if count+trialUseAmount > maxTrialCount {
		// 已达到试用上限
		u.opt.Log.Warnw("ConsumeTrial: trial limit reached", "key", key, "userID", userID, "count", count, "limit", maxTrialCount)
		return ErrTrialLimitReached
	}

	// 3. 递增计数
	if err := u.RecordCountUse(ctx, key, trialUseAmount); err != nil {
		u.opt.Log.Errorw("ConsumeTrial: failed to increment trial count", "key", key, "userID", userID, "error", err)
		return err
	}

	u.opt.Log.Infow("ConsumeTrial: successfully recorded trial", "key", key, "userID", userID, "count", count+trialUseAmount)
	return nil // 成功
}

// getSubscriptionQuota 根据付费周期获取对应的配额
func (u *UserBenefitService) getSubscriptionQuota(interval PaymentInterval) int {
	switch interval {
	case PaymentIntervalMonth, PaymentIntervalThreeMonth, PaymentIntervalSixMonth:
		return monthlySubscriptionQuota
	case PaymentIntervalYear:
		return yearlySubscriptionQuota
	default:
		return weeklySubscriptionQuota
	}
}

// CheckAndCanUse 检查用户是否可以使用功能，并消费对应配额
// 返回使用的计数键和可能的错误
func (u *UserBenefitService) CheckAndCanUse(ctx context.Context, userid int, loc *time.Location) (string, error) {
	userID := strconv.Itoa(userid)
	if userID == "" {
		return "", errors.New("deviceID cannot be empty")
	}

	// 1. 尝试使用试用额度
	trialKey := fmt.Sprintf(trialKeyPrefix, userID)
	opCtx, cancel := context.WithTimeout(ctx, shortOperationTimeout)
	defer cancel()

	err := u.ConsumeTrial(opCtx, userID)
	if err == nil {
		// 试用次数记录成功，返回试用键
		return trialKey, nil
	}

	// 如果不是因为达到试用上限导致的错误，直接返回错误
	if !errors.Is(err, ErrTrialLimitReached) {
		u.opt.Log.Errorw("CheckAndCanUse: failed to consume trial with unexpected error", "userID", userID, "error", err)
		return "", err
	}

	// 2. 检查订阅额度（如果试用额度已用完）
	isPremium, paymentInfo := u.paymentSrv.GetPaymentInfoByUserID(ctx, userid)
	if !isPremium || paymentInfo.SubID == "" {
		u.opt.Log.Infow("CheckAndCanUse: user is not premium or has no subscription", "userID", userID)
		return "", ErrNoSubscription
	}

	// 构造订阅额度的key
	subKey := fmt.Sprintf(subscriptionKeyPrefix, paymentInfo.SubID)

	// 根据付费周期获取配额上限
	interval := PaymentInterval(paymentInfo.Interval)
	maxQuota := u.getSubscriptionQuota(interval)

	// 检查当前使用次数
	count, err := u.GetOrInitQuotaUsage(ctx, subKey, loc, interval, userid, paymentInfo)
	if err != nil {
		u.opt.Log.Errorw("CheckAndCanUse: failed to get subscription usage", "userID", userID, "subID", paymentInfo.SubID, "interval", interval, "error", err)
		return "", err
	}

	// 检查是否达到订阅额度上限
	if count >= maxQuota {
		u.opt.Log.Infow("CheckAndCanUse: subscription quota limit reached", "userID", userID, "subID", paymentInfo.SubID, "interval", interval, "count", count, "limit", maxQuota)
		return "", ErrQuotaLimitReached
	}

	// 递增使用次数，每次消耗subscriptionUseAmount点
	if err := u.RecordCountUse(ctx, subKey, subscriptionUseAmount); err != nil {
		u.opt.Log.Errorw("CheckAndCanUse: failed to increment subscription usage", "userID", userID, "subID", paymentInfo.SubID, "amount", subscriptionUseAmount, "error", err)
		return "", err
	}

	u.opt.Log.Infow("CheckAndCanUse: successfully recorded subscription usage", "userID", userID, "subID", paymentInfo.SubID, "interval", interval, "count", count+subscriptionUseAmount)
	return subKey, nil
}

// GetOrInitQuotaUsage 根据付费类型获取或初始化配额使用情况
func (u *UserBenefitService) GetOrInitQuotaUsage(ctx context.Context, key string, loc *time.Location, interval PaymentInterval, userID int, paymentInfo *response.PaymentInfoResp) (int, error) {
	// 检查用户订阅类型变更
	intervalKey := fmt.Sprintf(userIntervalKeyPrefix, userID)

	// 获取用户之前的订阅类型
	oldIntervalStr, err := u.opt.Redis.Get(ctx, intervalKey).Result()

	// 初始化oldInterval变量
	oldInterval := PaymentInterval("")
	hasOldInterval := false

	if err == nil {
		// 键存在且成功获取
		oldInterval = PaymentInterval(oldIntervalStr)
		hasOldInterval = true
	} else if !errors.Is(err, redis.Nil) {
		// 发生除了键不存在之外的其他错误
		u.opt.Log.Warnw("GetOrInitQuotaUsage: error getting previous interval", "userID", userID, "error", err)
	}
	// 如果是redis.Nil错误，表示键不存在，此时oldInterval保持为空字符串，hasOldInterval为false

	// 存储当前订阅类型以便将来检测变更
	if oldInterval != interval {
		if err := u.opt.Redis.Set(ctx, intervalKey, string(interval), 0).Err(); err != nil {
			u.opt.Log.Warnw("GetOrInitQuotaUsage: failed to save current interval", "userID", userID, "interval", interval, "error", err)
		}
	}

	// 获取当前计数
	n, err := u.opt.Redis.Get(ctx, key).Int()

	// 处理订阅类型变更
	if hasOldInterval && oldInterval != "" && oldInterval != interval {
		// 查询支付记录获取详细信息
		payment, err := u.opt.PaymentRepo.FindBySubID(ctx, paymentInfo.SubID)
		if err != nil {
			u.opt.Log.Errorw("GetOrInitQuotaUsage: failed to get payment record", "userID", userID, "subID", paymentInfo.SubID, "error", err)
			return 0, err
		}

		// 从extra字段解析upgradeTime
		var extraData struct {
			UpgradeTime int64 `json:"upgradeTime"`
		}

		if err := json.Unmarshal([]byte(payment.Extra), &extraData); err != nil {
			u.opt.Log.Warnw("GetOrInitQuotaUsage: failed to parse extra data", "userID", userID, "subID", paymentInfo.SubID, "extra", payment.Extra, "error", err)
			// 解析失败，将使用备选时间源
			extraData.UpgradeTime = 0
		}

		// 使用升级时间或备选时间源
		var upgradeTime time.Time
		// 判断upgradeTime是否有效（大于0且大于createTime）
		createTimeUnix := int64(payment.CreateTime) // 假设CreateTime是Unix时间戳类型
		if extraData.UpgradeTime > 0 && extraData.UpgradeTime > createTimeUnix {
			upgradeTime = time.Unix(extraData.UpgradeTime, 0)
			u.opt.Log.Infow("GetOrInitQuotaUsage: using upgradeTime for calculation",
				"userID", userID, "upgradeTime", upgradeTime)
		} else {
			// 如果upgradeTime无效或小于createTime，使用当前时间
			upgradeTime = time.Now()
			u.opt.Log.Infow("GetOrInitQuotaUsage: using current time for calculation",
				"userID", userID, "now", upgradeTime)
		}

		// 处理订阅升级情况
		if oldInterval == PaymentIntervalWeek && (interval == PaymentIntervalMonth || interval == PaymentIntervalYear) {
			// 周 → 月/年：清零日改为升级当日的次月同日
			nextTime := u.getMonthlyNextTime(upgradeTime)
			expire := time.Until(nextTime)

			// 设置新的TTL，但保留当前使用量
			if err := u.opt.Redis.Expire(ctx, key, expire).Err(); err != nil {
				u.opt.Log.Errorw("GetOrInitQuotaUsage: failed to update expiration after upgrade", "userID", userID, "key", key, "error", err)
			} else {
				u.opt.Log.Infow("GetOrInitQuotaUsage: updated quota for weekly to monthly/yearly upgrade",
					"userID", userID, "oldInterval", oldInterval, "newInterval", interval, "expire", expire)
			}

			return n, nil
		} else if oldInterval == PaymentIntervalMonth && interval == PaymentIntervalYear {
			// 月 → 年：保持原月订清零日
			// 不需要更改TTL，保持现有过期时间
			u.opt.Log.Infow("GetOrInitQuotaUsage: monthly to yearly upgrade, keeping original expiry",
				"userID", userID, "oldInterval", oldInterval, "newInterval", interval)

			return n, nil
		}
	}

	// 处理键不存在的情况，需要创建新键
	if errors.Is(err, redis.Nil) {
		var expire time.Duration

		// 根据付费周期确定过期时间类型
		switch interval {
		case PaymentIntervalMonth, PaymentIntervalThreeMonth, PaymentIntervalSixMonth, PaymentIntervalYear:
			// 不再重复获取paymentInfo
			createTime := time.Unix(paymentInfo.CreateTime, 0)

			// 使用从创建时间计算的下月同日
			nextTime := u.getMonthlyNextTime(createTime)
			expire = time.Until(nextTime)
		default:
			// 周订阅使用标准周期
			// 不再重复获取paymentInfo
			createTime := time.Unix(paymentInfo.CreateTime, 0)

			// 使用从创建时间计算的下周同日
			nextTime := u.getWeeklyNextTime(createTime)
			expire = time.Until(nextTime)
		}

		if expire <= 0 {
			u.opt.Log.Errorw("GetOrInitQuotaUsage: calculated zero or negative expiration", "key", key, "interval", interval, "expire", expire)
			expire = 24 * time.Hour
		}

		_, setErr := u.opt.Redis.Set(ctx, key, 0, expire).Result()
		if setErr != nil {
			u.opt.Log.Errorw("GetOrInitQuotaUsage: failed to set new key", "key", key, "expire", expire, "error", setErr)
			return 0, setErr
		}
		u.opt.Log.Infow("GetOrInitQuotaUsage: created new key", "key", key, "count", 0, "expire", expire)
		return 0, nil
	} else if err != nil {
		// 其他Redis错误
		u.opt.Log.Errorw("GetOrInitQuotaUsage: failed to get key", "key", key, "error", err)
		return 0, err
	}

	// 键存在且没有发生订阅变更，返回当前计数
	return n, nil
}

// GetUsageByDeviceID 查询用户已使用的额度
func (u *UserBenefitService) GetUsageByDeviceID(ctx context.Context, userid int) (*response.UserUsageInfo, error) {
	userID := strconv.Itoa(userid)
	if userID == "" {
		return nil, errors.New("deviceID cannot be empty")
	}

	// 创建超时上下文
	opCtx, cancel := context.WithTimeout(ctx, shortOperationTimeout)
	defer cancel()

	// 先创建基本结果对象，默认值为试用配额
	result := &response.UserUsageInfo{
		TrialTotal:        maxTrialCount,
		SubscriptionTotal: 0, // 将根据用户的订阅类型设置
	}

	// 1. 获取试用信息
	if err := u.fillTrialInfo(opCtx, userID, result); err != nil {
		return result, err
	}

	// 2. 检查付费信息
	isPremium, paymentInfo := u.paymentSrv.GetPaymentInfoByUserID(ctx, userid)
	if !isPremium || paymentInfo.SubID == "" {
		// 非付费用户，只返回试用信息
		return result, nil
	}

	// 3. 获取订阅信息
	interval := PaymentInterval(paymentInfo.Interval)
	result.SubscriptionTotal = u.getSubscriptionQuota(interval)
	result.Interval = string(interval)

	// 4. 填充订阅使用量和过期时间
	if err := u.fillSubscriptionInfo(ctx, opCtx, userID, paymentInfo, interval, result); err != nil {
		return result, err
	}

	return result, nil
}

// fillTrialInfo 获取并填充试用信息
func (u *UserBenefitService) fillTrialInfo(ctx context.Context, userID string, result *response.UserUsageInfo) error {
	trialKey := fmt.Sprintf(trialKeyPrefix, userID)
	trialCount, err := u.opt.Redis.Get(ctx, trialKey).Int()

	if err != nil && !errors.Is(err, redis.Nil) {
		u.opt.Log.Errorw("GetUsageByDeviceID: failed to get trial count", "userID", userID, "error", err)
		return err
	}

	if errors.Is(err, redis.Nil) {
		trialCount = 0
	} else {
		// 获取试用 key 的 TTL
		ttl, err := u.opt.Redis.TTL(ctx, trialKey).Result()
		if err == nil && ttl > 0 { // ttl 返回 -2 表示 key 不存在，-1 表示没有设置过期时间
			result.TrialExpireTime = time.Now().Add(ttl).Unix()
		} else if err != nil {
			u.opt.Log.Warnw("GetUsageByDeviceID: failed to get trial key TTL", "userID", userID, "key", trialKey, "error", err)
		}
	}

	result.TrialUsed = trialCount
	return nil
}

// fillSubscriptionInfo 获取并填充订阅信息
func (u *UserBenefitService) fillSubscriptionInfo(ctx, opCtx context.Context, userID string, paymentInfo *response.PaymentInfoResp, interval PaymentInterval, result *response.UserUsageInfo) error {
	// 获取已使用的订阅额度
	subKey := fmt.Sprintf(subscriptionKeyPrefix, paymentInfo.SubID)
	subCount, getErr := u.opt.Redis.Get(opCtx, subKey).Int()

	if getErr != nil && !errors.Is(getErr, redis.Nil) {
		u.opt.Log.Errorw("GetUsageByDeviceID: failed to get subscription count",
			"userID", userID, "subID", paymentInfo.SubID, "error", getErr)
		return getErr
	}

	// 记录键是否存在
	keyExists := !errors.Is(getErr, redis.Nil)

	// 检查并处理订阅类型变更
	if err := u.handleSubscriptionTypeChange(ctx, userID, paymentInfo, interval, subKey, keyExists, result); err != nil {
		return err
	}

	// 如果键不存在，计算未使用订阅的过期时间
	if !keyExists {
		subCount = 0
		referenceTime := u.getSubscriptionReferenceTime(ctx, userID, paymentInfo)

		// 根据当前订阅类型计算过期时间
		var nextTime time.Time

		switch interval {
		case PaymentIntervalMonth, PaymentIntervalThreeMonth, PaymentIntervalSixMonth, PaymentIntervalYear:
			nextTime = u.getMonthlyNextTime(referenceTime)
		default:
			nextTime = u.getWeeklyNextTime(referenceTime)
		}

		result.SubscriptionExpireTime = nextTime.Unix()
		u.opt.Log.Infow("GetUsageByDeviceID: calculated expire time for unused subscription",
			"userID", userID, "subID", paymentInfo.SubID, "interval", interval,
			"expireTime", nextTime)
	} else {
		// 键存在，获取TTL
		ttl, ttlErr := u.opt.Redis.TTL(opCtx, subKey).Result()
		if ttlErr == nil {
			if ttl > 0 {
				// 正常的过期时间
				result.SubscriptionExpireTime = time.Now().Add(ttl).Unix()
			} else if ttl == -1 {
				// 键存在但没有设置过期时间，设置一个合理的过期时间
				u.opt.Log.Warnw("GetUsageByDeviceID: key exists without expiry",
					"userID", userID, "subID", paymentInfo.SubID)

				// 计算合理的过期时间
				referenceTime := u.getSubscriptionReferenceTime(ctx, userID, paymentInfo)
				var nextTime time.Time

				switch interval {
				case PaymentIntervalMonth, PaymentIntervalThreeMonth, PaymentIntervalSixMonth, PaymentIntervalYear:
					nextTime = u.getMonthlyNextTime(referenceTime)
				default:
					nextTime = u.getWeeklyNextTime(referenceTime)
				}

				expire := time.Until(nextTime)
				if expire <= 0 {
					expire = 24 * time.Hour // 默认至少一天
				}

				// 为键设置过期时间
				if expireErr := u.opt.Redis.Expire(ctx, subKey, expire).Err(); expireErr != nil {
					u.opt.Log.Warnw("GetUsageByDeviceID: failed to set missing expiry",
						"userID", userID, "key", subKey, "error", expireErr)
				}

				result.SubscriptionExpireTime = nextTime.Unix()
			}
		} else {
			u.opt.Log.Warnw("GetUsageByDeviceID: failed to get subscription key TTL",
				"userID", userID, "subID", paymentInfo.SubID, "key", subKey, "error", ttlErr)
		}
	}

	result.SubscriptionUsed = subCount
	return nil
}

// handleSubscriptionTypeChange 处理订阅类型变更
func (u *UserBenefitService) handleSubscriptionTypeChange(ctx context.Context, userID string, paymentInfo *response.PaymentInfoResp,
	interval PaymentInterval, subKey string, keyExists bool, result *response.UserUsageInfo) error {

	// 检查用户订阅类型变更
	intervalKey := fmt.Sprintf(userIntervalKeyPrefix, userID)
	oldIntervalStr, redisErr := u.opt.Redis.Get(ctx, intervalKey).Result()
	oldInterval := PaymentInterval("")

	if redisErr == nil {
		oldInterval = PaymentInterval(oldIntervalStr)

		// 检查到订阅类型变更
		if oldInterval != interval {
			u.opt.Log.Infow("GetUsageByDeviceID: subscription type changed",
				"userID", userID, "oldInterval", oldInterval, "newInterval", interval)

			// 更新Redis中的订阅类型
			if setErr := u.opt.Redis.Set(ctx, intervalKey, string(interval), 0).Err(); setErr != nil {
				u.opt.Log.Warnw("GetUsageByDeviceID: failed to update interval",
					"userID", userID, "error", setErr)
			}

			// 如果存在计数key且订阅类型变更，需要重设过期时间
			if keyExists {
				referenceTime := u.getSubscriptionReferenceTime(ctx, userID, paymentInfo)

				// 根据新的订阅类型计算过期时间
				var nextTime time.Time

				switch interval {
				case PaymentIntervalMonth, PaymentIntervalThreeMonth, PaymentIntervalSixMonth, PaymentIntervalYear:
					nextTime = u.getMonthlyNextTime(referenceTime)
				default:
					nextTime = u.getWeeklyNextTime(referenceTime)
				}

				expire := time.Until(nextTime)
				if expire <= 0 {
					expire = 24 * time.Hour // 至少保证有一天的过期时间
				}

				// 更新过期时间，保留当前使用量
				if expireErr := u.opt.Redis.Expire(ctx, subKey, expire).Err(); expireErr != nil {
					u.opt.Log.Warnw("GetUsageByDeviceID: failed to update key expiration",
						"userID", userID, "subKey", subKey, "error", expireErr)
				} else {
					u.opt.Log.Infow("GetUsageByDeviceID: updated key expiration due to subscription change",
						"userID", userID, "oldInterval", oldInterval, "newInterval", interval,
						"expire", expire, "expireTime", nextTime)
				}

				// 更新结果中的过期时间
				result.SubscriptionExpireTime = nextTime.Unix()
			}
		}
	} else if !errors.Is(redisErr, redis.Nil) {
		u.opt.Log.Warnw("GetUsageByDeviceID: error getting previous interval",
			"userID", userID, "error", redisErr)
	} else {
		// 键不存在，存储当前订阅类型
		if setErr := u.opt.Redis.Set(ctx, intervalKey, string(interval), 0).Err(); setErr != nil {
			u.opt.Log.Warnw("GetUsageByDeviceID: failed to save interval",
				"userID", userID, "interval", interval, "error", setErr)
		}
	}

	return nil
}

// getSubscriptionReferenceTime 获取订阅的参考时间
func (u *UserBenefitService) getSubscriptionReferenceTime(ctx context.Context, userID string, paymentInfo *response.PaymentInfoResp) time.Time {
	// 查询支付记录获取参考时间
	payment, paymentErr := u.opt.PaymentRepo.FindBySubID(ctx, paymentInfo.SubID)
	if paymentErr == nil && payment != nil {
		var extraData struct {
			UpgradeTime int64 `json:"upgradeTime"`
		}

		if unmarshalErr := json.Unmarshal([]byte(payment.Extra), &extraData); unmarshalErr == nil &&
			extraData.UpgradeTime > 0 && extraData.UpgradeTime > int64(payment.CreateTime) {
			// 使用有效的upgradeTime
			referenceTime := time.Unix(extraData.UpgradeTime, 0)
			u.opt.Log.Infow("GetUsageByDeviceID: using upgradeTime for calculation",
				"userID", userID, "upgradeTime", referenceTime)
			return referenceTime
		}

		// 无有效的upgradeTime，使用createTime
		referenceTime := time.Unix(paymentInfo.CreateTime, 0)
		u.opt.Log.Infow("GetUsageByDeviceID: using createTime for calculation",
			"userID", userID, "createTime", referenceTime)
		return referenceTime
	}

	// 无法获取支付记录，直接使用createTime
	referenceTime := time.Unix(paymentInfo.CreateTime, 0)
	if paymentErr != nil {
		u.opt.Log.Warnw("GetUsageByDeviceID: failed to get payment record, using createTime",
			"userID", userID, "subID", paymentInfo.SubID, "error", paymentErr)
	}

	return referenceTime
}

func (u *UserBenefitService) getMonthlyNextTime(userChangeStart time.Time) time.Time {
	now := time.Now()
	month := xtime.MonthBetween(now, userChangeStart)
	next := userChangeStart.AddDate(0, month, 0)
	if next.Before(now.Add(time.Minute)) {
		next = next.AddDate(0, 1, 0)
	}
	return next
}

// getWeeklyNextTime 计算从指定时间点到下一个周期性清零时间
func (u *UserBenefitService) getWeeklyNextTime(userChangeStart time.Time) time.Time {
	now := time.Now()
	// 计算从userChangeStart到now之间经过了多少个完整的周
	weeks := xtime.WeekBetween(now, userChangeStart)
	// 将userChangeStart增加相应的周数，得到当前周期的起始日期
	next := userChangeStart.AddDate(0, 0, weeks*7)

	// 如果计算出的next时间已经过去（或即将过去），则增加一周
	if next.Before(now.Add(time.Minute)) {
		next = next.AddDate(0, 0, 7)
	}

	return next
}
