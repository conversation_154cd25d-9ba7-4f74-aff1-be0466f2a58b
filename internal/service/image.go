package service

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/Sider-ai/go-pkg/xfile"
	"github.com/Sider-ai/go-pkg/xsync"
	"github.com/google/uuid"
	"github.com/sashabaranov/go-openai"

	openaiClient "imagezero/internal/clients/openai"
	"imagezero/internal/data/ent"
	"imagezero/internal/dto/request"
	"imagezero/internal/dto/response"
	"imagezero/internal/routes/common"
	"imagezero/pkg/ecode"
)

type ImageSrv struct {
	Options
	userBenefitService *UserBenefitService
}

func NewImageSrv(opts Options, userBenefitService *UserBenefitService) *ImageSrv {
	return &ImageSrv{Options: opts, userBenefitService: userBenefitService}
}

func (i *ImageSrv) Process(ctx context.Context, r *request.ImageRequest) (*response.ImageUUIDResponse, error) {

	if r == nil {
		return nil, errors.New("request object is nil")
	}

	v := ctx.Value(common.CurrentUserInfoKey)
	if v == nil {
		return nil, errors.New(common.CurrentUserInfoKey + " not in context")
	}
	user, ok := v.(*ent.User)
	if !ok {
		return nil, errors.New(common.CurrentUserInfoKey + " not in context")
	}

	v = ctx.Value(common.RequestTraceIDKey)
	if v == nil {
		return nil, errors.New(common.RequestTraceIDKey + " not in context")
	}
	traceID, ok := v.(string)
	if !ok {
		return nil, errors.New(common.RequestTraceIDKey + " not in context")
	}

	loc := r.GetLocation()

	// 检查用户权益并扣费
	countKey, err := i.userBenefitService.CheckAndCanUse(ctx, user.ID, loc)
	if err != nil {
		// 根据错误类型返回对应的错误码
		if errors.Is(err, ecode.FeatureUseUp) {
			return nil, ecode.FeatureUseUp
		} else if errors.Is(err, ecode.RateLimitExceeded) {
			return nil, ecode.RateLimitExceeded
		} else if errors.Is(err, ecode.FeatureNeedPremiumUse) {
			return nil, ecode.FeatureNeedPremiumUse
		}
		// 其他未知错误仍然包装并返回
		return nil, fmt.Errorf("failed to check user benefit: %w", err)
	}

	var processErr error

	// 生成UUID
	fileUUID := uuid.New().String()
	i.Options.Log.Infow("Generated UUID for image processing", "uuid", fileUUID)

	// 在Redis中设置初始状态为pending
	statusMap := map[string]interface{}{
		"status": "pending",
	}

	err = i.Options.Redis.HSet(ctx, fileUUID, statusMap).Err()
	if err != nil {
		i.Options.Log.Errorw("Failed to set initial status in Redis", "uuid", fileUUID, "error", err)
		processErr = fmt.Errorf("failed to set initial status: %w", err)
		return nil, processErr
	}

	// 设置2天的过期时间
	err = i.Options.Redis.Expire(ctx, fileUUID, 2*24*time.Hour).Err()
	if err != nil {
		i.Options.Log.Errorw("Failed to set TTL in Redis", "uuid", fileUUID, "error", err)
		// 不中断流程，只记录日志
	}

	// 使用xsync.SafeGoRoutine异步处理图像
	xsync.SafeGoRoutine(func() {

		defer func() {
			newCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
			defer cancel()
			if processErr != nil {

				err := i.UserRequestRepo.RequestFailed(newCtx, traceID, 500, processErr.Error())
				if err != nil {
					i.Options.Log.Errorw("Failed to request failed", "traceID", traceID, "error", err)
				}

				// 如果处理失败，恢复已扣除的次数
				rollbackErr := i.userBenefitService.RecordCountUseFail(newCtx, countKey, 10)
				if rollbackErr != nil {
					i.Options.Log.Errorw("Failed to rollback usage count", "userID", user.ID, "key", countKey, "error", rollbackErr)
				}
			} else {
				err := i.UserRequestRepo.RequestCompleted(newCtx, traceID, 200, "")
				if err != nil {
					i.Options.Log.Errorw("Failed to request Complete", "traceID", traceID, "error", err)
				}
			}
		}()

		defer func() {
			if r := recover(); r != nil {
				processErr = errors.New(fmt.Sprint(r))
				panic(r)
			}
		}()

		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
		defer cancel()
		var clientResp openai.ImageResponse
		var err error

		if len(r.Image) == 0 {
			var req openai.ImageRequest
			req, err = r.ToClientGenerateReq()
			if err != nil {
				processErr = err

				i.RedisSetError(ctx, fileUUID, err)

				return
			}
			clientResp, err = i.Options.OpenaiClient.Generate(ctx, req)
			if err != nil {
				processErr = fmt.Errorf("process failed: %w", err)

				i.RedisSetError(ctx, fileUUID, err)

				return
			}
		} else {
			var req openaiClient.ImageEditRequest
			req, err = r.ToClientEditReq()
			if err != nil {
				processErr = err

				i.RedisSetError(ctx, fileUUID, err)

				return
			}
			clientResp, err = i.Options.OpenaiClient.EditImage(ctx, req)
		}
		if err != nil {
			processErr = fmt.Errorf("process failed: %w", err)

			i.RedisSetError(ctx, fileUUID, err)

			return
		}

		resp := response.ClientRespToAPIResp(clientResp)
		if len(resp.Images) == 0 {
			processErr = errors.New("no image was successfully generated")

			i.RedisSetError(ctx, fileUUID, err)

			return
		}

		// 将响应转换为JSON并保存到文件
		jsonData, err := json.Marshal(resp)
		if err != nil {
			i.Options.Log.Errorw("Failed to marshal response to JSON", "error", err)
			processErr = fmt.Errorf("failed to marshal response: %w", err)

			i.RedisSetError(ctx, fileUUID, err)

			return
		}

		// 从配置获取存储路径
		examplePath := i.Options.Conf.ImageService.ResponseStoragePath

		// 创建日期目录
		dateDir := time.Now().Format("20060102") // 格式为YYYYMMDD
		responsePath := filepath.Join(examplePath, dateDir)

		// 确保目录存在
		if err := os.MkdirAll(responsePath, 0755); err != nil {
			i.Options.Log.Errorw("Failed to create directory", "path", responsePath, "error", err)
			processErr = fmt.Errorf("failed to create directory: %w", err)

			i.RedisSetError(ctx, fileUUID, err)

			return
		}

		// 构建完整文件路径
		filePath := filepath.Join(responsePath, fileUUID+".json")
		// 写入文件
		if err := os.WriteFile(filePath, jsonData, 0644); err != nil {
			i.Options.Log.Errorw("Failed to write response to file", "path", filePath, "error", err)
			processErr = fmt.Errorf("failed to write response to file: %w", err)

			i.RedisSetError(ctx, fileUUID, err)

			return
		}

		i.Options.Log.Infow("Response saved to file", "path", filePath)

		// 获取文件的绝对路径
		absFilePath, err := filepath.Abs(filePath)
		if err != nil {
			i.Options.Log.Errorw("Failed to get absolute file path", "path", filePath, "error", err)
			absFilePath = filePath // 如果获取绝对路径失败，使用原路径
		}

		// 更新Redis中的状态为ready并添加文件路径
		updateMap := map[string]interface{}{
			"status": "ready",
			"path":   absFilePath,
		}

		err = i.Options.Redis.HSet(ctx, fileUUID, updateMap).Err()
		if err != nil {
			i.Options.Log.Errorw("Failed to update status in Redis", "uuid", fileUUID, "error", err)
			processErr = fmt.Errorf("failed to update status: %w", err)

			i.RedisSetError(ctx, fileUUID, err)

			return
		}

		i.Options.Log.Infow("Processing completed successfully", "uuid", fileUUID)

	}, func(err error) {
		if err != nil {
			i.Options.Log.Errorw("Failed to process image", "uuid", fileUUID, "error", err)

			i.RedisSetError(ctx, fileUUID, err)

			// 出错时设置processErr以触发扣除次数的回滚
			processErr = err
		}
	})

	// 直接返回UUID
	return &response.ImageUUIDResponse{UUID: fileUUID}, nil
}

func (i *ImageSrv) GetStyleTemplate(ctx context.Context, r *request.TemplateRequest) (*response.TemplateResponse, error) {
	if r == nil {
		return nil, errors.New("request object is nil")
	}

	// // 获取分页数据
	// pageIndex := r.Data.PageIndex
	// pageSize := r.Data.PageSize

	// 查询数据，使用之前实现的分页查询
	templates, err := i.Options.StyleTemplateRepo.List(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to query templates: %w", err)
	}

	// // 获取符合条件的总记录数
	// totalCount, err := i.Options.StyleTemplateRepo.Count(ctx)
	// if err != nil {
	// 	return nil, fmt.Errorf("failed to count templates: %w", err)
	// }

	// 按模板类型分组
	typeGroups := make(map[string]*response.TemplateGroup)
	for _, template := range templates {
		templateType := template.TemplateType

		// 如果该类型的分组不存在，则创建新分组
		if _, exists := typeGroups[templateType]; !exists {
			typeGroups[templateType] = &response.TemplateGroup{
				GroupName: templateType,
				Templates: []response.Template{},
			}
		}

		// 将当前模板添加到对应分组
		typeGroups[templateType].Templates = append(
			typeGroups[templateType].Templates,
			convertToResponseTemplate(template),
		)
	}

	// 将分组转换为列表
	templateGroups := make([]response.TemplateGroup, 0, len(typeGroups))
	for _, group := range typeGroups {
		templateGroups = append(templateGroups, *group)
	}

	// 构建最终响应
	resp := &response.TemplateResponse{
		// TotalCount: totalCount,
		// PageIndex:  pageIndex,
		// PageSize:   pageSize,
		List: templateGroups,
	}

	return resp, nil
}

// convertToResponseTemplate 将数据库模板对象转换为响应模板对象
func convertToResponseTemplate(template *ent.StyleTemplate) response.Template {
	if template == nil {
		return response.Template{}
	}
	url := ""
	if len(template.URL) != 0 {
		url = template.URL[0]
	}
	return response.Template{
		ID:             template.ID,
		URL:            url,
		Prompt:         template.Prompt,
		Width:          template.Width,
		Height:         template.Height,
		LocalImageName: template.LocalImageName,
		VideoUrl:       template.VideoURL,
	}
}

// GetImageResponseByUUID 通过UUID从Redis获取文件路径，并读取解析为ImageResponse
func (i *ImageSrv) GetImageResponseByUUID(ctx context.Context, r *request.GetImageByUUIDRequest) (*response.ImageResponse, error) {
	if r == nil || r.UUID == "" {
		return nil, errors.New("uuid cannot be empty")
	}

	// 从Redis中获取记录
	result, err := i.Options.Redis.HGetAll(ctx, r.UUID).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get data from Redis: %w", err)
	}

	if len(result) == 0 {
		return nil, fmt.Errorf("record not found for UUID: %s", r.UUID)
	}

	// 检查处理状态
	status, ok := result["status"]
	if !ok {
		return nil, fmt.Errorf("invalid record format: status not found")
	}

	switch status {
	case "pending":
		return &response.ImageResponse{
			Status: "pending",
			Images: nil,
		}, nil
	case "failed":
		errorMsg := "image processing failed"
		if errMsg, ok := result["error"]; ok {
			errorMsg = errMsg
		}
		if strings.Contains(errorMsg, "safety") {
			return nil, ecode.StabilityContentModeration
		}
		return nil, fmt.Errorf("failed to process image: %s", errorMsg)
	case "ready":
		// 检查文件路径是否存在
		filePath, ok := result["path"]
		if !ok {
			return nil, fmt.Errorf("invalid record format: path not found")
		}

		// 检查文件是否存在
		if _, err := os.Stat(filePath); os.IsNotExist(err) {
			return nil, fmt.Errorf("file not found: %s", filePath)
		}

		// 读取文件内容
		fileData, err := os.ReadFile(filePath)
		if err != nil {
			return nil, fmt.Errorf("failed to read file: %w", err)
		}

		// 解析JSON内容为ImageResponse
		var resp response.ImageResponse
		if err := json.Unmarshal(fileData, &resp); err != nil {
			return nil, fmt.Errorf("failed to unmarshal response: %w", err)
		}

		return &resp, nil
	default:
		return nil, fmt.Errorf("unknown status: %s", status)
	}
}

func (i *ImageSrv) RedisSetError(ctx context.Context, uuid string, err error) {
	errorMap := map[string]interface{}{
		"status": "failed",
		"error":  err.Error(),
	}
	updateErr := i.Options.Redis.HSet(ctx, uuid, errorMap).Err()
	if updateErr != nil {
		i.Options.Log.Errorw("Failed to update error status in Redis", "uuid", uuid, "error", updateErr)
	}
}

// ReplaceAllStyleTemplates 删除所有现有样式模板并用新的替换
func (i *ImageSrv) ReplaceAllStyleTemplates(ctx context.Context, templates []*ent.StyleTemplate) ([]*ent.StyleTemplate, error) {
	i.Options.Log.Infow("Replacing all style templates", "templateCount", len(templates))
	return i.Options.StyleTemplateRepo.ReplaceAll(ctx, templates)
}

func (i *ImageSrv) UploadImage(ctx context.Context, objKey string, b []byte) error {
	mime, _, _ := xfile.GetFileMIME(bytes.NewReader(b))
	return i.Xoss.PutObject(ctx, "style-transfer/image/"+objKey, bytes.NewReader(b), mime)
}
