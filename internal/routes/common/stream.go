package common

import (
	"io"
	"strings"

	"github.com/gin-gonic/gin"
)

var (
	streamEnd = "[DONE]"
)

func Stream(c *gin.Context, dataChan <-chan any) {
	// flutter端需要关闭nginx的buffer功能
	if strings.Contains(<PERSON><PERSON>("User-Agent"), "ChitChatFlutter") {
		c<PERSON>("X-Accel-Buffering", "no")
	}
	c.Stream(func(w io.Writer) bool {
		data, ok := <-dataChan
		if !ok {
			return false
		}
		var result any = &Resp{}
		isContinue := true
		switch v := data.(type) {
		case error:
			_, result = NewResp(c, nil, v)
			isContinue = false
		case nil:
			//_, result = NewResp(streamEnd, nil)
			result = streamEnd
			isContinue = false
		default:
			_, result = NewResp(c, v, nil)
			isContinue = true
		}
		//bs, _ := jsoniter.Marshal(result)
		//_, _ = w.Write(append(bs, '\n'))
		c.SSEvent("", result)
		return isContinue
	})
}
