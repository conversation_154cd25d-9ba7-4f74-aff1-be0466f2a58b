package common

import (
	"context"
	"net/http"
	"reflect"

	"dario.cat/mergo"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"

	"imagezero/internal/data/ent"
	"imagezero/internal/dto/request"
)

// 抽象参数解析、验证、返回响应的逻辑，参考 https://www.willem.dev/articles/generic-http-handlers/

type ServiceFunc[in any, out any] func(context.Context, in) (out, error)

type ServiceFuncOnlyErr[in any] func(context.Context, in) error

type ServiceFuncWithUser[in any, out any] func(context.Context, *ent.User, in) (out, error)

type ServiceFuncWithUserOnlyErr[in any] func(context.Context, *ent.User, in) error

type HandlerWarpConf struct {
	OptionalLogin bool // 接口可以登录或者不登录，如果为true，会尝试获取当前登录用户，如果没有登录，user为nil
	ParseHashID   bool

	BindType     binding.Binding
	BindBodyType binding.BindingBody
	BindWithBody bool
	BindWithUri  bool
}

func getHandlerWarpConf(c *gin.Context, opts []HandlerWarpConf) *HandlerWarpConf {
	conf := &HandlerWarpConf{
		BindBodyType: binding.JSON,
		BindType:     binding.Default(c.Request.Method, c.ContentType()),
	}

	if c.Request.Method == http.MethodGet {
		conf.BindType = binding.Query
	}

	if len(opts) > 0 {
		_ = mergo.Merge(&conf, opts[0], mergo.WithOverride)
	}

	return conf
}

func initReq[in any]() in {
	var req in
	vType := reflect.TypeOf(req)
	if vType.Kind() == reflect.Ptr {
		newVal := reflect.New(vType.Elem())
		return newVal.Interface().(in)
	}
	return req
}

func setIPInfoToReq(c *gin.Context, req any) {
	if ipSetter, ok := req.(request.IPInfoSetter); ok {
		ipSetter.SetIPInfo(MustGetIPInfo(c))
	}
}

func handlerWarp[in any, out any](f any, opts ...HandlerWarpConf) gin.HandlerFunc {
	return func(c *gin.Context) {
		conf := getHandlerWarpConf(c, opts)

		var (
			req = initReq[in]()
			err error
		)
		switch {
		case conf.BindWithBody:
			err = c.ShouldBindBodyWith(&req, conf.BindBodyType)
		case conf.BindWithUri:
			if conf.ParseHashID {
				err = ShouldBindUriWithHashID(c, &req)
			} else {
				err = c.ShouldBindUri(&req)
			}
		default:
			if conf.ParseHashID {
				err = ShouldBindWithHashID(c, &req, conf.BindType)
			} else {
				err = c.ShouldBindWith(&req, conf.BindType)
			}
		}
		if err != nil {
			ParamsErrorResp(c, err)
			return
		}
		setIPInfoToReq(c, req)

		ctx := c.Request.Context()
		switch fn := f.(type) {
		case ServiceFunc[in, out]:
			WrapResp(c)(fn(ctx, req))
		case ServiceFuncOnlyErr[in]:
			WrapResp(c)(nil, fn(ctx, req))
		case ServiceFuncWithUser[in, out], ServiceFuncWithUserOnlyErr[in]:
			var user *ent.User
			if conf.OptionalLogin {
				user = GetCurrentUserInfo(c)
			} else {
				user = MustGetCurrentUserInfo(c)
			}
			switch ff := fn.(type) {
			case ServiceFuncWithUserOnlyErr[in]:
				WrapResp(c)(nil, ff(ctx, user, req))
			case ServiceFuncWithUser[in, out]:
				WrapResp(c)(ff(ctx, user, req))
			}
		default:
			panic("routes.common.handlerWarp: invalid service function")
		}
	}
}

func HandlerWarp[in any, out any](f ServiceFunc[in, out], opts ...HandlerWarpConf) gin.HandlerFunc {
	return handlerWarp[in, out](f, opts...)
}

func HandlerWarpOnlyErr[in any](f ServiceFuncOnlyErr[in], opts ...HandlerWarpConf) gin.HandlerFunc {
	return handlerWarp[in, error](f, opts...)
}

func HandlerWarpWithUser[in any, out any](f ServiceFuncWithUser[in, out], opts ...HandlerWarpConf) gin.HandlerFunc {
	return handlerWarp[in, out](f, opts...)
}

func HandlerWarpWithUserOnlyErr[in any](f ServiceFuncWithUserOnlyErr[in], opts ...HandlerWarpConf) gin.HandlerFunc {
	return handlerWarp[in, error](f, opts...)
}
