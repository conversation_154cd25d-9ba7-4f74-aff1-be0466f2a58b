package common

import (
	"fmt"
	"strings"

	"github.com/gin-gonic/gin"

	"imagezero/pkg/ecode"
)

//	gin: func defaultHandleRecovery(c *Context, err any) {
//		c.AbortWithStatus(http.StatusInternalServerError)
//	}
func HandleRecovery(c *gin.Context, err any) {
	ec := &ecode.Error{
		Code:     ecode.PanicCode,
		HttpCode: 500,
		Message:  "Internal Server Error",
	}
	param := newSaveAppLogParam(c, fmt.Errorf("panic error: %+v", err), *ec)
	go saveAppLog(param)
	resp := &Resp{
		Code:    ec.Code,
		Message: ec.Message,
		Data:    nil,
	}

	path := c.Request.URL.Path
	if strings.Contains(path, "/completion/text") {
		c.SSEvent("", resp)
	} else {
		c.JSON(ec.HttpCode, resp)
	}

	c.Abort()
}
