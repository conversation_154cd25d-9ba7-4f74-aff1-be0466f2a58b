package common

import (
	"slices"

	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/validator/v10"

	"imagezero/internal/clients/clientinfo"
)

func init() {
	validators := []func() (string, validator.Func){
		clientinfo.AppNameValidation,
		ChatFromValidation,
	}
	if v, ok := binding.Validator.Engine().(*validator.Validate); ok {
		for _, fn := range validators {
			if err := v.RegisterValidation(fn()); err != nil {
				panic(err)
			}
		}
	}
}

var validChatFrom = []string{"", "ask", "chat", "chatpdf", "newtab-chat", "quick-action", "quick-lookup", "search", "writing", "enhance-qa", "quickLookup", "translate", "translate_tools", "translate_tools_landingPage", "deepsearch", "ocr", "sider_ios_web", "finding_jobs", "video_summarize", "input-translation"}

func ChatFromValidation() (string, validator.Func) {
	return "chat_from", func(fl validator.FieldLevel) bool {
		val := fl.Field().String()
		return slices.Contains(validChatFrom, val)
	}
}
