package v1

import (
	"context"
	"errors"
	"io"
	"net/http"

	"github.com/gin-gonic/gin"

	"imagezero/internal/dto/request"
	"imagezero/internal/routes/common"
	"imagezero/internal/routes/middleware"
)

type ImageRoute struct {
	Options
}

func NewImageRoute(opt Options) *ImageRoute {
	return &ImageRoute{opt}
}

func (r *ImageRoute) RegisterRoute(router *gin.RouterGroup) {
	rg := router.Group("/v1/image")
	{
		rg.POST("/generate", middleware.TokenAuth(true, r.Jwt, r.UserRepo), middleware.UserRequestLogger(r.UserRequestRepo, r.Log), func() gin.HandlerFunc {
			return func(c *gin.Context) {
				if c != nil && c.Request != nil {
					v, ok := c.Get(common.CurrentUserInfoKey)
					if ok {
						ctx := context.WithValue(c.Request.Context(), common.CurrentUserInfoKey, v)
						c.Request = c.Request.WithContext(ctx)
					}
				}
			}
		}(), common.HandlerWarp(r.ImageSrv.Process))

		// 添加获取图像响应的接口
		rg.GET("/response/:uuid", func(c *gin.Context) {
			uuid := c.Param("uuid")
			if uuid == "" {
				c.JSON(http.StatusBadRequest, gin.H{
					"code": 1000,
					"msg":  "UUID parameter is required",
					"data": nil,
				})
				return
			}

			req := &request.GetImageByUUIDRequest{UUID: uuid}
			resp, err := r.ImageSrv.GetImageResponseByUUID(c.Request.Context(), req)
			common.WrapResp(c)(resp, err)
		})
	}

	// 样式模板相关接口
	stRg := router.Group("/v1/style_template")
	{
		stRg.GET("", common.HandlerWarp(r.ImageSrv.GetStyleTemplate))
	}
	stRg2 := router.Group("/v1/internal/style_template")
	{
		// 添加替换所有样式模板的接口
		stRg2.POST("/replace_all", common.HandlerWarp(r.ReplaceAllStyleTemplates))
		stRg2.POST("/image/upload", r.UploadImage)

	}
}

// ReplaceAllStyleTemplates 处理替换所有样式模板的请求
func (r *ImageRoute) ReplaceAllStyleTemplates(ctx context.Context, req request.ReplaceStyleTemplatesRequest) (interface{}, error) {
	return r.ImageSrv.ReplaceAllStyleTemplates(ctx, req.Templates)
}

// UploadImage 处理文件上传请求
func (r *ImageRoute) UploadImage(c *gin.Context) {

	// 获取文件
	file, _, err := c.Request.FormFile("file")
	if err != nil {
		common.WrapResp(c)(nil, err)
	}
	defer file.Close()

	// 读取文件内容
	fileBytes, err := io.ReadAll(file)
	if err != nil {
		common.WrapResp(c)(nil, err)
		return
	}

	// 检查是否提供了对象键
	objKey := c.PostForm("objKey")

	// 如果没有提供对象键，返回错误
	if objKey == "" {
		common.WrapResp(c)(nil, errors.New("objKey parameter is required"))
		return
	}

	// 调用服务层的上传方法
	err = r.ImageSrv.UploadImage(c.Request.Context(), objKey, fileBytes)
	if err != nil {
		common.WrapResp(c)(nil, err)
		return
	}

	// 返回上传成功的消息和文件对象键
	common.WrapResp(c)(gin.H{
		"URL": r.Config.OSS.Aws.Host + "/style-transfer/image/" + objKey,
	}, nil)
}
