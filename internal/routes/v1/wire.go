package v1

import (
	"github.com/bsm/redislock"
	"github.com/go-redis/redis_rate/v10"
	"go.uber.org/zap"

	"imagezero/configs"
	"imagezero/internal/clients/serverconf"
	"imagezero/internal/data"
	"imagezero/internal/service"

	"imagezero/pkg/jwt"
)

type Options struct {
	Limiter         *redis_rate.Limiter
	Locker          *redislock.Client
	Log             *zap.SugaredLogger
	Jwt             *jwt.JWT
	UserRepo        *data.UserRepo
	UserRequestRepo *data.UserRequestRepo
	Config          *configs.Config

	HealthSrv          *service.HealthSrv
	UserService        *service.UserService
	PaymentService     *service.PaymentService
	ApplePayService    *service.ApplePayService
	ImageSrv           *service.ImageSrv
	UserBenefitService *service.UserBenefitService
	ServerConfig       *serverconf.ServerConf
}
