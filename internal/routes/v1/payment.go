package v1

import (
	"io"
	"net/http"

	"imagezero/internal/data"
	"imagezero/internal/dto/request"
	"imagezero/internal/routes/common"
	"imagezero/internal/routes/middleware"
	"imagezero/internal/service"
	"imagezero/pkg/jwt"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type PaymentRoute struct {
	jwt             *jwt.JWT
	userRepo        *data.UserRepo
	log             *zap.SugaredLogger
	userService     *service.UserService
	paymentService  *service.PaymentService
	applePayService *service.ApplePayService
}

func NewPaymentRoute(opt *Options) *PaymentRoute {
	return &PaymentRoute{
		jwt:             opt.Jwt,
		userRepo:        opt.UserRepo,
		log:             opt.Log,
		userService:     opt.UserService,
		paymentService:  opt.PaymentService,
		applePayService: opt.ApplePayService,
	}
}

func (s *PaymentRoute) RegisterRoute(router *gin.RouterGroup) {
	r := router.Group("/v1/payment")
	{
		r.GET("/test", common.HandlerWarp(s.paymentService.Test))
		r.GET("/info", middleware.TokenAuth(true, s.jwt, s.userRepo), s.info)
		r.POST("/apple/verify", middleware.TokenAuth(true, s.jwt, s.userRepo), s.verifyV2)
		r.POST("/apple/notification", s.notification)
	}
}

func (s *PaymentRoute) info(c *gin.Context) {
	var req request.PaymentInfoReq
	if err := c.ShouldBindQuery(&req); err != nil {
		common.ParamsErrorResp(c, err)
		return
	}
	user := common.MustGetCurrentUserInfo(c)
	common.WrapResp(c)(s.paymentService.GetPaymentInfo(c.Request.Context(), user, &req))
}

func (s *PaymentRoute) verifyV2(c *gin.Context) {
	var req request.PaymentVerifyReqV2
	if err := c.ShouldBindJSON(&req); err != nil {
		common.ParamsErrorResp(c, err)
		return
	}
	ipInfo := common.MustGetIPInfo(c)
	user := common.MustGetCurrentUserInfo(c)
	common.WrapResp(c)(s.applePayService.PaymentVerifyV2(c.Request.Context(), user, ipInfo, &req))
}

func (s *PaymentRoute) notification(c *gin.Context) {
	w := c.Writer
	req := c.Request
	requestBytes, err := io.ReadAll(req.Body)
	if err != nil {
		s.log.Warnln("[Notification]Error.", err)
		w.WriteHeader(http.StatusServiceUnavailable)
		w.Write([]byte(err.Error()))
		return
	}
	defer req.Body.Close()

	s.log.Infoln("[notification]Webhook request.", len(requestBytes))
	err = s.applePayService.HandleNotification(c.Request.Context(), requestBytes)
	if err != nil {
		s.log.Warnln("[Notification]Handle webhook notification failed.", err)
		w.WriteHeader(http.StatusBadRequest)
		w.Write([]byte(err.Error()))
		return
	}

	w.WriteHeader(http.StatusOK)
	w.Write([]byte("OK"))
}
