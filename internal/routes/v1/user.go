package v1

import (
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/go-redis/redis_rate/v10"
	"go.uber.org/zap"

	"imagezero/configs"
	"imagezero/internal/clients/serverconf"
	"imagezero/internal/data"
	"imagezero/internal/dto/request"
	"imagezero/internal/routes/common"
	"imagezero/internal/routes/middleware"
	"imagezero/internal/service"
	"imagezero/pkg/jwt"
)

type UserRoute struct {
	log                *zap.SugaredLogger
	limiter            *redis_rate.Limiter
	jwt                *jwt.JWT
	userRepo           *data.UserRepo
	userService        *service.UserService
	userBenefitService *service.UserBenefitService
	configs            *configs.Config
	serverconf         *serverconf.ServerConf
}

func NewUserRoute(opt *Options) *UserRoute {
	return &UserRoute{
		log:                opt.Log,
		limiter:            opt.Limiter,
		jwt:                opt.Jwt,
		userService:        opt.UserService,
		userRepo:           opt.UserRepo,
		userBenefitService: opt.UserBenefitService,
		configs:            opt.Config,
		serverconf:         opt.ServerConfig,
	}
}

func (u *UserRoute) RegisterRoute(router *gin.RouterGroup) {
	auth := middleware.TokenAuth(true, u.jwt, u.userRepo)
	authNoMust := middleware.TokenAuth(false, u.jwt, u.userRepo)
	var key string
	u.serverconf.MustGet("device_secret", &key)
	user := router.Group("/v1/user")
	{
		user.POST("/login/device_id", middleware.HeaderValidationMiddleware(middleware.HeaderValidationConfig{
			Key:        key,
			HeaderName: "X-Device-ID",
			CtxKey:     "Device-ID",
		}), authNoMust, u.loginWithDeviceID)
		user.POST("/usage", auth, u.getUserUsageInfo)
		//user.POST("/check", authNoMust, u.check)
	}
}

func (u *UserRoute) loginWithDeviceID(c *gin.Context) {
	var req request.UserDeviceReq
	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		common.ParamsErrorResp(c, err)
		return
	}

	req.DeviceID = c.GetString("Device-ID")
	common.WrapResp(c)(u.userService.RegisterByDeviceID(c.Request.Context(), &req, common.MustGetIPInfo(c)))
}

func (u *UserRoute) getUserUsageInfo(c *gin.Context) {
	var req request.UserUsageInfoReq
	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		common.ParamsErrorResp(c, err)
		return
	}
	user := common.MustGetCurrentUserInfo(c)
	common.WrapResp(c)(u.userBenefitService.GetUsageByDeviceID(c.Request.Context(), user.ID))
}
