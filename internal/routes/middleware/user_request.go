package middleware

import (
	"bytes"
	"encoding/json"
	"io"
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"imagezero/internal/data"
	"imagezero/internal/routes/common"
)

// UserRequestLogger 中间件用于记录和跟踪用户请求
func UserRequestLogger(userRequestRepo *data.UserRequestRepo, log *zap.SugaredLogger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从上下文获取必要信息
		traceID := common.GetTraceID(c)
		userID := common.GetRequestUserID(c)
		method := c.Request.Method
		path := c.Request.URL.Path

		// 获取请求体数据
		var reqData string
		contentType := c.GetHeader("Content-Type")
		if contentType != "" && strings.Contains(contentType, "application/json") {
			// 备份原始请求体
			bodyBytes, _ := io.ReadAll(c.Request.Body)
			// 恢复请求体，供后续处理函数使用
			c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
			// 将请求体保存到上下文中
			c.Set(gin.BodyBytesKey, bodyBytes)

			// 记录请求数据
			if len(bodyBytes) > 0 {
				if len(bodyBytes) > 1024 {
					reqData = string(bodyBytes[:1024]) + "...(截断)"
				} else {
					reqData = string(bodyBytes)
				}
			}
		}

		// 如果请求体为空，尝试获取查询参数
		if reqData == "" && len(c.Request.URL.Query()) > 0 {
			queryMap := make(map[string][]string)
			for k, v := range c.Request.URL.Query() {
				queryMap[k] = v
			}
			queryBytes, err := json.Marshal(queryMap)
			if err == nil {
				if len(queryBytes) > 1024 {
					reqData = string(queryBytes[:1024]) + "...(截断)"
				} else {
					reqData = string(queryBytes)
				}
			}
		}

		// 修改中间件，仅复制请求体但不尝试绑定
		if _, ok := c.Get(gin.BodyBytesKey); !ok {
			// 读取完整请求体，而不是只读取1024字节
			bodyBytes, _ := io.ReadAll(c.Request.Body)
			if len(bodyBytes) > 0 {
				if len(bodyBytes) > 1024 {
					reqData = string(bodyBytes[:1024]) + "...(截断)"
				} else {
					reqData = string(bodyBytes)
				}
				// 重要：恢复完整请求体
				c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
			}
		}

		// 构造请求上下文
		ipInfo := common.GetIPInfo(c)
		reqCtx := data.RequestContext{
			ServiceName:     "",
			ServiceFunction: "",
		}

		// 尝试从请求头获取
		reqCtx.AppName = c.GetHeader("X-App-Name")
		reqCtx.AppVersion = c.GetHeader("X-App-Version")

		// 如果请求头中没有，尝试从URL参数获取
		if reqCtx.AppName == "" {
			reqCtx.AppName = c.Query("app_name")
		}
		if reqCtx.AppVersion == "" {
			reqCtx.AppVersion = c.Query("app_version")
		}

		// 如果请求头和URL参数中都没有，尝试从JSON body获取
		if (reqCtx.AppName == "" || reqCtx.AppVersion == "") && strings.Contains(c.GetHeader("Content-Type"), "application/json") {
			var jsonBody map[string]interface{}

			// 使用已绑定的请求体数据
			if cb, ok := c.Get(gin.BodyBytesKey); ok {
				if cbb, ok := cb.([]byte); ok && len(cbb) > 0 {
					if err := json.Unmarshal(cbb, &jsonBody); err == nil {
						// 检查并设置AppName
						if reqCtx.AppName == "" {
							if val, ok := jsonBody["app_name"].(string); ok {
								reqCtx.AppName = val
							}
						}

						// 检查并设置AppVersion
						if reqCtx.AppVersion == "" {
							if val, ok := jsonBody["app_version"].(string); ok {
								reqCtx.AppVersion = val
							}
						}
					}
				}
			}
		}

		// 设置IP和地区信息
		if ipInfo != nil {
			reqCtx.UserIP = ipInfo.IP
			reqCtx.UserRegion = ipInfo.CountryShort
		}

		// 创建初始请求记录
		_, err := userRequestRepo.CreateInitialRequest(c.Request.Context(), traceID, userID, method, path, "", reqCtx)
		if err != nil {
			// 仅记录日志，不中断请求
			log.Error("create initial request failed", "error", err)
			// 这里可以使用您的日志系统记录错误
		}

		// 创建自定义响应写入器
		writer := &responseWriter{
			ResponseWriter: c.Writer,
			body:           []byte{},
		}
		c.Writer = writer

		// 处理请求
		c.Next()

	}
}

// responseWriter 是一个自定义的响应写入器，用于捕获响应体
type responseWriter struct {
	gin.ResponseWriter
	body []byte
}

// Write 重写Write方法以捕获响应体
func (w *responseWriter) Write(b []byte) (int, error) {
	// 如果尚未截断且当前体积小于限制
	if len(w.body) < 1024 && !bytes.HasSuffix(w.body, []byte("...(截断)")) {
		remaining := 1024 - len(w.body)
		if len(b) > remaining {
			// 只添加剩余空间的数据
			w.body = append(w.body, b[:remaining]...)
			// 添加截断标记
			w.body = append(w.body, []byte("...(截断)")...)
		} else {
			// 完全添加
			w.body = append(w.body, b...)
		}
	}
	return w.ResponseWriter.Write(b)
}

// WriteString 重写WriteString方法以捕获响应体
func (w *responseWriter) WriteString(s string) (int, error) {
	// 如果尚未截断且当前体积小于限制
	if len(w.body) < 1024 && !bytes.HasSuffix(w.body, []byte("...(截断)")) {
		remaining := 1024 - len(w.body)
		if len(s) > remaining {
			// 只添加剩余空间的数据
			w.body = append(w.body, []byte(s[:remaining])...)
			// 添加截断标记
			w.body = append(w.body, []byte("...(截断)")...)
		} else {
			// 完全添加
			w.body = append(w.body, []byte(s)...)
		}
	}
	return w.ResponseWriter.WriteString(s)
}
