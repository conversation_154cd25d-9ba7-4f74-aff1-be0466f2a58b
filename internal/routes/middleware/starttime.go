package middleware

import (
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
)

type respTimeWriter struct {
	gin.ResponseWriter
	start time.Time
}

func (w *respTimeWriter) WriteHeader(statusCode int) {
	w.Header().Add("X-Response-Time", fmt.Sprintf("%d ms", time.Now().Sub(w.start).Milliseconds()+1))
	w.ResponseWriter.WriteHeader(statusCode)
}

func StartTime() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		ctx.Writer = &respTimeWriter{ResponseWriter: ctx.Writer, start: time.Now()}
	}
}
