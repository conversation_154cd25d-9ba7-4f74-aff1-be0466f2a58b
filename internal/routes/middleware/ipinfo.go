package middleware

import (
	"cmp"
	"net"
	"slices"

	"github.com/Sider-ai/go-pkg/ginutil"
	"github.com/gin-gonic/gin"
	"github.com/oschwald/maxminddb-golang"
	"go.uber.org/zap"

	"imagezero/internal/dto/request"
	"imagezero/internal/routes/common"
	"imagezero/pkg/ecode"
	"imagezero/pkg/ipinfo"
)

var cidrs = []string{
	"************/20",
	"************/22",
	"************/22",
	"**********/22",
	"************/18",
	"*************/18",
	"************/20",
	"************/20",
	"*************/22",
	"************/17",
	"***********/15",
	"**********/13",
	"**********/14",
	"**********/13",
	"**********/22",
	"2400:cb00::/32",
	"2606:4700::/32",
	"2803:f800::/32",
	"2405:b500::/32",
	"2405:8100::/32",
	"2a06:98c0::/29",
	"2c0f:f248::/32",
}

func SetIPInfo(log *zap.SugaredLogger, ipCli *maxminddb.Reader) gin.HandlerFunc {
	return func(c *gin.Context) {
		ip := common.GetRealIP(c)
		ipInfo := &request.IPInfo{IP: ip}
		ipInfo.CountryShort = GetCFIPCountry(c)

		if ipInfo.CountryShort == "" {
			countryShort, err := ipinfo.GetIP(ipCli, ip)
			if err != nil {
				log.Errorf("middleware SetIPInfo get client ip err, ip: %s, err: %s", ip, err)
			}
			ipInfo.CountryShort = cmp.Or(countryShort, "-")
		}

		//c.Set(common.IpInfoKey, ipInfo)
		ginutil.SetContextValue(c, common.IpInfoKey, ipInfo)
		c.Next()
	}
}

func GetCFIPCountry(c *gin.Context) string {
	return c.Request.Header.Get("CF-IPCountry")
}

// 检查IP是否在给定的CIDR范围内
func isCFIPInRange(ip string) bool {
	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return false
	}

	for _, cidr := range cidrs {
		_, ipnet, err := net.ParseCIDR(cidr)
		if err != nil {
			continue
		}
		if ipnet.Contains(parsedIP) {
			return true
		}
	}
	return false
}

func NotSupportRegion(restrictedCountryShorts []string) gin.HandlerFunc {
	return func(c *gin.Context) {
		ipInfo := common.GetIPInfo(c)
		if slices.Contains(restrictedCountryShorts, ipInfo.CountryShort) {
			common.ErrorResp(c, ecode.NotSupportRegion)
		}
		return
	}

}
