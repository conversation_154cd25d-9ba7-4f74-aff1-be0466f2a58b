package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis_rate/v10"
	"github.com/google/wire"
	"github.com/oschwald/maxminddb-golang"
	"go.uber.org/zap"

	"imagezero/configs"
	"imagezero/internal/clients/hashid"
	"imagezero/internal/data"
	v1 "imagezero/internal/routes/v1"

	"imagezero/pkg/jwt"
)

var ProviderSet = wire.NewSet(
	wire.Struct(new(Options), "*"),
	wire.Struct(new(v1.Options), "*"),
	NewEngine,
	NewHttpEngine,
	v1.NewHealthRoute,
	v1.NewPaymentRoute,
	v1.NewImageRoute,
	v1.NewUserRoute,
)

type Options struct {
	Router  *gin.Engine
	Conf    *configs.Config
	Limiter *redis_rate.Limiter
	IPDB    *maxminddb.Reader
	Jwt     *jwt.JWT
	Log     *zap.SugaredLogger

	Payment    *v1.PaymentRoute
	Image      *v1.ImageRoute
	Health     *v1.HealthRoute
	User       *v1.UserRoute
	AppLogRepo *data.AppLogRepo
	HashID     *hashid.HashID
	UserRequestRepo *data.UserRequestRepo
}
