package response

type PaymentInfoResp struct {
	PlanName           string `json:"plan_name"`
	PlanID             int    `json:"plan_id"`
	PaymentMethod      string `json:"payment_method"`
	Interval           string `json:"interval"`
	CreateTime         int64  `json:"create_time"`
	NextBillingTime    int64  `json:"next_billing_time"`
	SubscriptionStatus string `json:"subscription_status"`
	PayStatus          string `json:"pay_status"`
	CustomerLink       string `json:"customer_link"`
	CustomerEmail      string `json:"customer_email"`
	SubID              string `json:"-"`
	IsPaidBefore       bool   `json:"is_paid_before"`
	PaidUserID         int    `json:"-"`
	IsGift             bool   `json:"is_gift"`
	IsTrial            bool   `json:"is_trial"`
	IsIOS              bool   `json:"is_ios"`
}
type PaymentVerifyResp struct {
	PaymentInfoResp
}
