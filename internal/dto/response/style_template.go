package response

// TemplateResponse represents a response containing templates with pagination
type TemplateResponse struct {
	// // TotalCount is the total number of templates available
	// TotalCount int `json:"totalCount"`
	//
	// // PageIndex is the current page number, starting from 1
	// PageIndex int `json:"pageIndex"`
	//
	// // PageSize is the number of items per page
	// PageSize int `json:"pageSize"`

	// List contains template data grouped by type
	List []TemplateGroup `json:"list"`
}

// TemplateGroup represents a group of templates by type
type TemplateGroup struct {

	// GroupName is the display name of the template type
	GroupName string `json:"groupName"`

	// Templates contains the list of templates in this group
	Templates []Template `json:"templates"`
}

// Template represents a single template item
type Template struct {
	// ID is the unique identifier for the template
	ID string `json:"id"`

	// URL is the remote resource URL for the template
	URL string `json:"url"`

	// Prompt is an optional AI prompt used to generate images
	Prompt string `json:"prompt,omitempty"`

	Width          int    `json:"width"`
	Height         int    `json:"height"`
	LocalImageName string `json:"localImageName,omitempty"`
	VideoUrl       string `json:"videoUrl,omitempty"`
}
