package response

import (
	"github.com/Sider-ai/go-pkg/xfile"
	"github.com/sasha<PERSON>nov/go-openai"
)

type ImageResponse struct {
	Status string  `json:"status"`
	Images []Image `json:"images"`
}

// ImageUUIDResponse 返回图像处理的UUID
type ImageUUIDResponse struct {
	UUID string `json:"uuid"`
}

type Image struct {
	Base64 string `json:"base64"`
	Width  int    `json:"width"`
	Height int    `json:"height"`
	Size   int    `json:"size"`
}

func ClientRespToAPIResp(resp openai.ImageResponse) *ImageResponse {
	var imgs []Image
	for _, img := range resp.Data {
		w, h, err := xfile.GetImageDimensionForBase64(img.B64JSON)
		if err != nil {
			w, h = 0, 0
		}
		imgs = append(imgs, Image{
			Base64: img.B64JSON,
			Width:  w,
			Height: h,
			Size:   len(img.B64JSO<PERSON>),
		})
	}
	return &ImageResponse{
		Status: "ready",
		Images: imgs,
	}
}
