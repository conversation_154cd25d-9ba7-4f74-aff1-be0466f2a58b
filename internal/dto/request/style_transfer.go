package request

// UserDeviceReq represents the request for user device operation
type UserDeviceReq struct {
	ClientInfo
	DeviceID string `json:"device_id,omitempty"`
}

type UserUsageInfoReq struct {
	ClientInfo
}

type UserRestoreReq struct {
	ClientInfo
	Key string `json:"key" binding:"required"`
}

type IPInfo struct {
	IP           string
	CountryShort string
	// CountryLong  string
	// Region       string // 省份
	// City         string
	// Isp          string
}
