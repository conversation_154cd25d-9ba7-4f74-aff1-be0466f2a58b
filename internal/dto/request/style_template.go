package request

// TemplateRequest represents a request for template data with pagination
type TemplateRequest struct {
	// Data contains the request parameters
	Data struct {
		// PageIndex is the requested page number, starting from 1
		PageIndex int `json:"pageIndex"`

		// PageSize is the number of items per page
		PageSize int `json:"pageSize"`

		// TypeID is an optional filter for template type
		// 0: Poster, 1: Knowledge card, 2: General style
		TypeID int `json:"typeId"`
	} `json:"data"`
}
