package request

import (
	"bytes"
	"errors"
	"fmt"
	"io"
	"mime/multipart"

	"github.com/Sider-ai/go-pkg/xfile"
	"github.com/sashabaranov/go-openai"

	openaiClient "imagezero/internal/clients/openai"
	"imagezero/internal/data/ent"
)

type ImageRequest struct {
	TimeZone

	Prompt         string `json:"prompt,omitempty" form:"prompt"`
	Model          string `json:"model,omitempty" form:"model"`
	N              int    `json:"n,omitempty" form:"n"`
	Quality        string `json:"quality,omitempty" form:"quality"`
	AspectRatio    string `json:"aspect_ratio,omitempty" form:"aspect_ratio"`
	Size           string `json:"size,omitempty" form:"size"`
	Style          string `json:"style,omitempty" form:"style"`
	ResponseFormat string `json:"response_format,omitempty" form:"response_format"`
	User           string `json:"user,omitempty" form:"user"`

	Image []*multipart.FileHeader `json:"image" form:"image"`
	Mask  *multipart.FileHeader   `json:"mask" form:"mask"`
}

func (i *ImageRequest) ToClientGenerateReq() (openai.ImageRequest, error) {

	if i == nil {
		return openai.ImageRequest{}, errors.New("invalid request: nil")
	}

	if i.Prompt == "" {
		return openai.ImageRequest{}, errors.New("invalid request: empty prompt")
	}

	switch i.Model {
	case "":
		i.Model = "gpt-image-1"
	}

	switch {
	case i.N > 10:
		i.N = 10
	case i.N < 1:
		i.N = 1
	}

	switch i.Quality {
	case "low", "medium", "high", "auto":
	default:
		i.Quality = "auto"
	}

	if i.AspectRatio != "" && i.Size == "" {
		switch i.AspectRatio {
		case "1:1":
			i.Size = "1024x1024"
		case "2:3":
			i.Size = "1024x1536"
		case "3:2":
			i.Size = "1536x1024"
		default:
			i.Size = "auto"
		}
	}

	switch i.Size {
	case "1024x1024", "1024:1536", "1536x1024", "auto":
	default:
		i.Size = "auto"
	}

	return openai.ImageRequest{
		Prompt:  i.Prompt,
		Model:   i.Model,
		N:       i.N,
		Quality: i.Quality,
		Size:    i.Size,
	}, nil
}

func (i *ImageRequest) ToClientEditReq() (openaiClient.ImageEditRequest, error) {

	imageReqest, err := i.ToClientGenerateReq()
	if err != nil {
		return openaiClient.ImageEditRequest{}, err
	}

	result := openaiClient.ImageEditRequest{
		ImageRequest: imageReqest,
	}

	for _, img := range i.Image {
		clientReqImg, err := FileHeaderToReqImage(img)
		if err != nil {
			return openaiClient.ImageEditRequest{}, err
		}
		result.Image = append(result.Image, clientReqImg)
	}

	if i.Mask != nil {
		mask, err := FileHeaderToReqImage(i.Mask)
		if err != nil {
			return openaiClient.ImageEditRequest{}, err
		}
		result.Mask = mask
	}

	return result, nil

}

func FileHeaderToReqImage(img *multipart.FileHeader) (*openaiClient.ReqImage, error) {
	file, err := img.Open()
	if err != nil {
		return nil, fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()

	buf := bytes.NewBuffer(nil)
	if _, err := io.Copy(buf, file); err != nil {
		return nil, fmt.Errorf("failed to read file: %w", err)
	}

	imgBytes := buf.Bytes()
	mime, _, err := xfile.GetFileMIME(bytes.NewReader(imgBytes))
	if err != nil {
		mime = ""
	}

	w, h, err := xfile.GetImageDimensionForByteData(imgBytes)
	if err != nil {
		w, h = 0, 0
	}

	return &openaiClient.ReqImage{
		Data:     imgBytes,
		FileName: img.Filename,
		Mime:     mime,
		Width:    w,
		Height:   h,
	}, nil
}

// GetImageByUUIDRequest 通过UUID获取图像响应的请求
type GetImageByUUIDRequest struct {
	UUID string `uri:"uuid" binding:"required"`
}

// ReplaceStyleTemplatesRequest 替换所有样式模板的请求
type ReplaceStyleTemplatesRequest struct {
	Templates []*ent.StyleTemplate `json:"templates"`
}
