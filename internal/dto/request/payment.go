package request

type PaymentInfoReq struct {
	From string `json:"from" form:"from"`

	AppName    string `json:"app_name" form:"app_name"`
	AppVersion string `json:"app_version" form:"app_version"`
}

type PaymentVerifyReqV2 struct {
	ClientInfo
	IsRecover             int    `json:"is_recover" form:"is_recover"`
	ProductID             string `json:"product_id" form:"product_id"`
	OriginalTransactionID string `json:"original_transaction_id" form:"original_transaction_id"`
	TransactionID         string `json:"transaction_id" form:"transaction_id"`
	Environment           string `json:"environment" form:"environment"`
	PromotionName         string `json:"promotion_name" form:"promotion_name"`
}
