openapi: 3.0.3
info:
  title: ImageZero API
  description: API for generating and editing images using AI models.
  version: 1.0.0
servers:
  - url: / # Assuming the API is served at the root
    description: Development server

paths:
  /health:
    get:
      summary: Health Check
      description: Checks the health status of the service.
      tags:
        - Health
      responses:
        '200':
          description: Service is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: pong
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/image/generate:
    post:
      summary: Generate or Edit Image
      description: |
        Generates a new image based on a text prompt or edits an existing image using a prompt and an optional mask.
        Accepts `multipart/form-data` for requests involving image uploads (editing).
      tags:
        - Image
      requestBody:
        description: Image generation parameters and optional image/mask files for editing.
        required: true
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ImageRequest'
            # Example for multipart/form-data (cannot be fully represented in pure YAML schema, needs tooling support)
            # Example values might need manual adjustment in tools like Swagger UI or Postman.
            encoding:
              image:
                contentType: image/png, image/jpeg, image/webp # Example content types
              mask:
                contentType: image/png # Masks often require transparency

      responses:
        '200':
          description: Successfully generated or edited image(s).
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ImageResponse'
        '400':
          description: Bad Request - Invalid parameters provided (e.g., missing prompt).
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error - Failed to process the request.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

components:
  schemas:
    ImageRequest:
      type: object
      required:
        - prompt
      properties:
        prompt:
          type: string
          description: The text prompt describing the desired image.
          example: "A cute cat wearing a samurai helmet"
        model:
          type: string
          description: The AI model to use for generation.
          default: "gpt-image-1"
          example: "gpt-image-1"
        n:
          type: integer
          format: int32
          description: The number of images to generate.
          minimum: 1
          maximum: 10
          default: 1
          example: 1
        quality:
          type: string
          description: The quality of the generated image.
          enum: ["low", "medium", "high", "auto"]
          default: "auto"
          example: "high"
        aspect_ratio:
          type: string
          description: "Desired aspect ratio. If provided, 'size' will be inferred ('1:1' -> '1024x1024', '2:3' -> '1024x1536', '3:2' -> '1536x1024')."
          enum: ["1:1", "2:3", "3:2"]
          example: "1:1"
        size:
          type: string
          description: "The dimensions of the generated image. Overrides 'aspect_ratio' if both are provided. 'auto' lets the model decide."
          enum: ["1024x1024", "1024:1536", "1536x1024", "auto"]
          default: "auto"
          example: "1024x1024"
        style:
          type: string
          description: The style of the generated image (e.g., 'vivid', 'natural'). Specific options depend on the model.
          example: "vivid"
        response_format:
          type: string
          description: The format in which the generated images are returned. Currently only supports base64 JSON.
          default: "b64_json"
          enum: ["b64_json"] # Assuming based on response DTO
        user:
          type: string
          description: An identifier for the end-user making the request.
          example: "user-12345"
        image:
          type: array
          items:
            type: string
            format: binary
          description: The source image file(s) for editing. Required for edit operations.
        mask:
          type: string
          format: binary
          description: An optional mask file (e.g., PNG with transparency) indicating areas to edit in the source image. If not provided, the entire image is used.

    ImageResponse:
      type: object
      properties:
        images:
          type: array
          items:
            $ref: '#/components/schemas/Image'

    Image:
      type: object
      properties:
        base64:
          type: string
          format: byte # Often used for base64 encoded strings in OpenAPI
          description: Base64 encoded image data.
        width:
          type: integer
          format: int32
          description: Width of the image in pixels.
        height:
          type: integer
          format: int32
          description: Height of the image in pixels.
        size:
          type: integer
          format: int64 # Use int64 for potential large sizes
          description: Size of the base64 encoded data in bytes.

    Error:
      type: object
      properties:
        code:
          type: integer
          format: int32
          description: Error code. # Consider standardizing error codes (e.g., using RFC 7807 Problem Details)
        message:
          type: string
          description: Detailed error message.
      required:
        - code
        - message 