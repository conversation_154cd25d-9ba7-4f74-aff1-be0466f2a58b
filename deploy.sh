#!/bin/bash
source ~/.bash_profile
source ~/.bashrc
echo "================================================"
echo "Deploy starting..."
echo "Project: style transfer backend"
read -p "Please enter server [dev]: " server
read -p "Please enter branch: " branch
echo "================================================"
if [ "$server" == "dev" ]; then
  deploy_to="dog@3.83.33.127"
else
  echo "Invalid server"
  exit 1
fi

ssh -t $deploy_to "/var/www/deploy/style-transfer-backend/deploy-style-transfer-backend.sh $server $branch"
