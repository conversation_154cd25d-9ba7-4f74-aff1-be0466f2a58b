package ipinfo

import (
	"errors"
	"net"

	"github.com/oschwald/maxminddb-golang"
)

func GetIP(db *maxminddb.Reader, clientIP string) (string, error) {
	if db == nil || clientIP == "" {
		return "", errors.New("Get IP failed")
	}
	ip := net.ParseIP(clientIP)

	var record struct {
		Country struct {
			ISOCode string `maxminddb:"iso_code"`
		} `maxminddb:"country"`
	} // Or any appropriate struct

	err := db.Lookup(ip, &record)
	if err != nil {
		return "", err
	}

	return record.Country.ISOCode, nil
}
