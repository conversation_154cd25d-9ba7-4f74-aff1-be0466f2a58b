package kv

import (
	"encoding/json"
	"fmt"

	"github.com/spf13/cast"
)

type KV map[string]any

func (kv KV) GetString(key string) string {
	if val, ok := kv[key]; ok {
		return cast.ToString(val)
	}
	return ""
}

func (kv KV) GetBool(key string) bool {
	if val, ok := kv[key]; ok {
		return cast.ToBool(val)
	}
	return false
}

func (kv KV) GetInt(key string) int {
	if val, ok := kv[key]; ok {
		return cast.ToInt(val)
	}
	return 0
}

func New() KV {
	return make(map[string]any)
}

func FromMap(m map[string]any) KV {
	if m == nil {
		return New()
	}
	return m
}

func FromJson(s string) KV {
	if s == "" {
		return New()
	}
	var kv KV
	err := json.Unmarshal([]byte(s), &kv)
	if err != nil {
		return nil
	}

	return kv
}

func (kv KV) Len() int {
	return len(kv)
}
func (kv KV) Clone() KV {
	return Join(kv)
}
func (kv KV) Put(k string, v interface{}) KV {
	kv[k] = v
	return kv
}
func (kv KV) Get(k string) *Reply {
	v, ok := kv[k]
	return newReply(k, v, ok)
}
func (kv KV) Fetch(k string) any {
	if v, ok := kv[k]; ok {
		return v
	}
	return nil
}
func (kv KV) Has(k string) bool {
	_, ok := kv[k]
	return ok
}
func (kv KV) Marshal() ([]byte, error) {
	return json.Marshal(kv)
}

func (kv KV) String() string {
	marshal, err := json.Marshal(kv)
	if err != nil {
		return ""
	}
	return string(marshal)
}

func (kv KV) Unmarshal(v interface{}) error {
	marshal, err := kv.Marshal()
	if err != nil {
		return err
	}
	return json.Unmarshal(marshal, v)
}

func Join(kvs ...KV) KV {
	out := KV{}
	for _, kv := range kvs {
		for k, v := range kv {
			out[k] = v
		}
	}
	return out
}

func Pairs(kvs ...interface{}) KV {
	if len(kvs)%2 == 1 {
		panic(fmt.Sprintf("KV: Pairs got the odd number of input pairs for KV: %d", len(kvs)))
	}
	kv := KV{}
	var key string
	for i, s := range kvs {
		if i%2 == 0 {
			key = s.(string)
			continue
		}
		kv[key] = s
	}
	return kv
}
