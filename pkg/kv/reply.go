package kv

import (
	"errors"

	"github.com/spf13/cast"
)

var NotFound = errors.New("not found")

type Reply struct {
	k  string
	v  interface{}
	ok bool
}

func newReply(k string, v interface{}, ok bool) *Reply {
	return &Reply{
		k:  k,
		v:  v,
		ok: ok,
	}
}

func (r *Reply) String() (string, error) {
	if !r.ok {
		return "", NotFound
	}
	return cast.ToStringE(r.v)
}
func (r *Reply) Int64() (int64, error) {
	if !r.ok {
		return 0, NotFound
	}
	return cast.ToInt64E(r.v)
}
func (r *Reply) Float64() (float64, error) {
	if !r.ok {
		return 0, NotFound
	}
	return cast.ToFloat64E(r.v)
}
func (r *Reply) Bool() (bool, error) {
	if !r.ok {
		return false, NotFound
	}
	return cast.ToBoolE(r.v)
}
func (r *Reply) Value() (interface{}, error) {
	if !r.ok {
		return nil, NotFound
	}
	return r.v, nil
}
