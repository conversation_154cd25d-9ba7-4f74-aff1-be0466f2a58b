package xtime

import (
	"fmt"
	"time"
	_ "time/tzdata"

	"github.com/duke-git/lancet/v2/datetime"
)

const (
	ISO8601Format = "2006-01-02T15:04:05Z"
)
const (
	UnixSecond int64 = 1
	UnixMinute       = 60 * UnixSecond
	UnixHour         = 60 * UnixMinute
	UnixDay          = 24 * UnixHour
	UnixWeek         = 7 * UnixDay
)

var (
	CNLocation, _ = time.LoadLocation("Asia/Shanghai")
)

// StartOfDayWithLoc Returns the start of the day
func StartOfDayWithLoc(loc *time.Location) time.Time {
	now := time.Now().In(loc)
	y, m, d := now.Date()
	return time.Date(y, m, d, 0, 0, 0, 0, loc)
}

type PeriodTimeStamp struct {
	Start int64
	End   int64
}

type PeriodTime struct {
	Start time.Time
	End   time.Time
}

func FormatGMTISO8601(t time.Time) string {
	return t.UTC().Format(ISO8601Format)
}

func FormatSecondsDuration(t float64) string {
	seconds := int(t)
	ms := int(t*1000) % 1000
	sec := seconds % 60
	seconds = seconds / 60
	m := seconds % 60
	h := seconds / 60
	return fmt.Sprintf("%02d:%02d:%02d.%03d", h, m, sec, ms)
}

func EndOfDayWithLoc(loc *time.Location) time.Time {
	now := time.Now().In(loc)
	return datetime.EndOfDay(now)
}

func StartOfMonthWithLoc(loc *time.Location) time.Time {
	now := time.Now().In(loc)
	return datetime.EndOfMonth(now)
}

func NowToEndOfDayDuration(loc *time.Location) time.Duration {
	now := time.Now().In(loc)
	end := datetime.EndOfDay(now)
	return end.Sub(now)
}

func NowToEndOfWeekDuration(loc *time.Location) time.Duration {
	now := time.Now().In(loc)
	end := datetime.EndOfWeek(now)
	return end.Sub(now)
}

func NowToEndOfMonthDuration(loc *time.Location) time.Duration {
	now := time.Now().In(loc)
	end := datetime.EndOfMonth(now)
	return end.Sub(now)
}

// 求两个时间的月份差值
func MonthBetween(a, b time.Time) int {
	if a.Location() != b.Location() {
		b = b.In(a.Location())
	}
	if a.After(b) {
		a, b = b, a
	}
	yearDiff := b.Year() - a.Year()
	monthDiff := b.Month() - a.Month()

	return yearDiff*12 + int(monthDiff)
}

// 求两个时间的周数差值
func WeekBetween(a, b time.Time) int {
	if a.Location() != b.Location() {
		b = b.In(a.Location())
	}
	if a.After(b) {
		a, b = b, a
	}
	
	// 将时间调整到各自周的开始（周一）
	aWeekStart := a.AddDate(0, 0, -int(a.Weekday())+1)
	if a.Weekday() == time.Sunday {
		aWeekStart = a.AddDate(0, 0, -6)
	}
	
	bWeekStart := b.AddDate(0, 0, -int(b.Weekday())+1)
	if b.Weekday() == time.Sunday {
		bWeekStart = b.AddDate(0, 0, -6)
	}
	
	// 计算两个周开始日期之间的天数差
	days := int(bWeekStart.Sub(aWeekStart).Hours() / 24)
	
	// 将天数差转换为周数差
	return days / 7
}

// example: defer TrackTime("exec")()
func TrackTime(key string) func() {
	pre := time.Now()
	return func() {
		elapsed := time.Since(pre)
		fmt.Printf("\n%s elapsed:%v\n", key, elapsed)
	}
}
