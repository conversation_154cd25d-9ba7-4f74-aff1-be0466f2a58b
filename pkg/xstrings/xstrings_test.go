package xstrings

import (
	"testing"
	"unicode/utf8"
)

func TestRemoveInvalidStrPrefix(t *testing.T) {
	arr := []string{"啦啦啦", "🐶", "🐱", "🐶🏠", "🀄️", "😄", "一二三"}
	for _, a := range arr {
		b := a[1:]
		if utf8.ValidString(b) {
			t.<PERSON>("error", a, b)
		}
		b = RemoveInvalidStrPrefix(b, 0)
		if !utf8.ValidString(b) {
			t.<PERSON><PERSON>("error", a, b)
		}
		t.Log("a:", a, "b:", b)
	}

	c := RemoveInvalidStrPrefix("", 0)
	t.<PERSON>g("c", c)
}

func FuzzRemoveInvalidStrPrefix(f *testing.F) {
	arr := []string{"啦啦啦", "🐶", "🐱", "🐶🏠", "🀄️", "😄", "一二三", "hello"}
	for i, a := range arr {
		f.Add(i, a)
	}
	f.Fuzz(func(t *testing.T, i int, s string) {
		b := s
		if len(b) > 1 {
			b = s[1:]
		}
		r := RemoveInvalidStrPrefix(b, 0)
		if !utf8.ValidString(r) {
			t.Error("error", s, b, r)
		}
	})
}
