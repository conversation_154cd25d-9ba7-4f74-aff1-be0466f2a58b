package xstrings

import (
	"strings"
	"unicode/utf8"
	"unsafe"
)

var (
	englishChinesePunctuation = map[string]string{
		",": "，",
		":": "：",
		"!": "！",
	}
)

// 移除字符串最前面的无效的uft8编码字符
func RemoveInvalidStrPrefix(s string, max int) string {
	if max == 0 {
		max = 4
	}
	n := 0
	for {
		n++
		if n > max {
			return s
		}
		if len(s) == 0 {
			return ""
		}
		if utf8.ValidString(s) {
			return s
		}
		s = s[1:]
	}
}

// StringToBytes converts string to byte slice without a memory allocation.
// For more details, see https://github.com/golang/go/issues/53003#issuecomment-1140276077.
func StringToBytes(s string) []byte {
	return unsafe.Slice(unsafe.StringData(s), len(s))
}

// BytesToString converts byte slice to string without a memory allocation.
// For more details, see https://github.com/golang/go/issues/53003#issuecomment-1140276077.
func BytesToString(b []byte) string {
	if len(b) == 0 {
		return ""
	}
	return unsafe.String(&b[0], len(b))
}

func ConvertPunctuationToChinese(str string) string {
	for en, cn := range englishChinesePunctuation {
		str = strings.ReplaceAll(str, en, cn)
	}
	return str
}
