package iap

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
)

const (
	// SandboxURL is the endpoint for sandbox environment.
	IAPVerifySandboxURL string = "https://sandbox.itunes.apple.com/verifyReceipt"
	// ProductionURL is the endpoint for production environment.
	IAPVerifyProductionURL string = "https://buy.itunes.apple.com/verifyReceipt"
	// ContentType is the request content-type for apple store.
	IAPRequestContentType string = "application/json; charset=utf-8"

	IAPSandbox    string = "Sandbox"
	IAPProduction string = "Production"

	IAP_SUBSCRIPTION_AUTO_RENEW_OFF = 0
	IAP_SUBSCRIPTION_AUTO_RENEW_ON  = 1

	// 通知类型常量
	// https://developer.apple.com/documentation/appstoreservernotifications/notificationtype
	IAP_NOTIFICATION_DID_RECOVER               = "DID_RECOVER"
	IAP_NOTIFICATION_CONSUMPTION_REQUEST       = "CONSUMPTION_REQUEST"
	IAP_NOTIFICATION_DID_CHANGE_RENEWAL_PREF   = "DID_CHANGE_RENEWAL_PREF"
	IAP_NOTIFICATION_DID_CHANGE_RENEWAL_STATUS = "DID_CHANGE_RENEWAL_STATUS"
	IAP_NOTIFICATION_DID_FAIL_TO_RENEW         = "DID_FAIL_TO_RENEW"
	IAP_NOTIFICATION_DID_RENEW                 = "DID_RENEW"
	IAP_NOTIFICATION_EXPIRED                   = "EXPIRED"
	IAP_NOTIFICATION_GRACE_PERIOD_EXPIRED      = "GRACE_PERIOD_EXPIRED"
	IAP_NOTIFICATION_OFFER_REDEEMED            = "OFFER_REDEEMED"
	IAP_NOTIFICATION_PRICE_INCREASE            = "PRICE_INCREASE"
	IAP_NOTIFICATION_REFUND                    = "REFUND"
	IAP_NOTIFICATION_REFUND_DECLINED           = "REFUND_DECLINED"
	IAP_NOTIFICATION_RENEWAL_EXTENDED          = "RENEWAL_EXTENDED"
	IAP_NOTIFICATION_REVOKE                    = "REVOKE"
	IAP_NOTIFICATION_SUBSCRIBED                = "SUBSCRIBED"
	IAP_NOTIFICATION_TEST                      = "TEST"

	// 子类型常量
	// https://developer.apple.com/documentation/appstoreservernotifications/subtype
	IAP_NOTIFICATION_SUB_INITIAL_BUY         = "INITIAL_BUY"
	IAP_NOTIFICATION_SUB_RESUBSCRIBE         = "RESUBSCRIBE"
	IAP_NOTIFICATION_SUB_DOWNGRADE           = "DOWNGRADE"
	IAP_NOTIFICATION_SUB_UPGRADE             = "UPGRADE"
	IAP_NOTIFICATION_SUB_AUTO_RENEW_ENABLED  = "AUTO_RENEW_ENABLED"
	IAP_NOTIFICATION_SUB_AUTO_RENEW_DISABLED = "AUTO_RENEW_DISABLED"
	IAP_NOTIFICATION_SUB_VOLUNTARY           = "VOLUNTARY"
	IAP_NOTIFICATION_SUB_BILLING_RETRY       = "BILLING_RETRY"
	IAP_NOTIFICATION_SUB_PRICE_INCREASE      = "PRICE_INCREASE"
	IAP_NOTIFICATION_SUB_GGRACE_PERIOD       = "GRACE_PERIOD"
	IAP_NOTIFICATION_SUB_BILLING_RECOVERY    = "BILLING_RECOVERY"
	IAP_NOTIFICATION_SUB_PENDING             = "PENDING"
	IAP_NOTIFICATION_SUB_ACCEPTED            = "ACCEPTED"
	IAP_NOTIFICATION_SUB_DEFAULT             = ""
)

type (
	// IAPRequest is struct
	// https://developer.apple.com/library/content/releasenotes/General/ValidateAppStoreReceipt/Chapters/ValidateRemotely.html
	// The IAPRequest type has the request parameter
	IAPRequest struct {
		ReceiptData string `json:"receipt-data"`
		// Only used for receipts that contain auto-renewable subscriptions.
		Password string `json:"password,omitempty"`
		// Only used for iOS7 style app receipts that contain auto-renewable or non-renewing subscriptions.
		// If value is true, response includes only the latest renewal transaction for any subscriptions.
		ExcludeOldTransactions bool `json:"exclude-old-transactions"`
	}

	// The ReceiptCreationDate type indicates the date when the app receipt was created.
	ReceiptCreationDate struct {
		CreationDate    string `json:"receipt_creation_date"`
		CreationDateMS  string `json:"receipt_creation_date_ms"`
		CreationDatePST string `json:"receipt_creation_date_pst"`
	}

	// The ReceiptExpirationDate type indicates the date when the app receipt was expired.
	ReceiptExpirationDate struct {
		ExpirationDate    string `json:"expiration_date"`
		ExpirationDateMS  string `json:"expiration_date_ms"`
		ExpirationDatePST string `json:"expiration_date_pst"`
	}

	// The RequestDate type indicates the date and time that the request was sent
	RequestDate struct {
		RequestDate    string `json:"request_date"`
		RequestDateMS  string `json:"request_date_ms"`
		RequestDatePST string `json:"request_date_pst"`
	}

	// The PurchaseDate type indicates the date and time that the item was purchased
	PurchaseDate struct {
		PurchaseDate    string `json:"purchase_date"`
		PurchaseDateMS  string `json:"purchase_date_ms"`
		PurchaseDatePST string `json:"purchase_date_pst"`
	}

	// The OriginalPurchaseDate type indicates the beginning of the subscription period
	OriginalPurchaseDate struct {
		OriginalPurchaseDate    string `json:"original_purchase_date"`
		OriginalPurchaseDateMS  string `json:"original_purchase_date_ms"`
		OriginalPurchaseDatePST string `json:"original_purchase_date_pst"`
	}

	// The PreorderDate type indicates the date and time that the pre-order
	PreorderDate struct {
		PreorderDate    string `json:"preorder_date"`
		PreorderDateMS  string `json:"preorder_date_ms"`
		PreorderDatePST string `json:"preorder_date_pst"`
	}

	// The ExpiresDate type indicates the expiration date for the subscription
	ExpiresDate struct {
		ExpiresDate             string `json:"expires_date,omitempty"`
		ExpiresDateMS           string `json:"expires_date_ms,omitempty"`
		ExpiresDatePST          string `json:"expires_date_pst,omitempty"`
		ExpiresDateFormatted    string `json:"expires_date_formatted,omitempty"`
		ExpiresDateFormattedPST string `json:"expires_date_formatted_pst,omitempty"`
	}

	// The CancellationDate type indicates the time and date of the cancellation by Apple customer support
	CancellationDate struct {
		CancellationDate    string `json:"cancellation_date,omitempty"`
		CancellationDateMS  string `json:"cancellation_date_ms,omitempty"`
		CancellationDatePST string `json:"cancellation_date_pst,omitempty"`
	}

	// The GracePeriodDate type indicates the grace period date for the subscription
	GracePeriodDate struct {
		GracePeriodDate    string `json:"grace_period_expires_date,omitempty"`
		GracePeriodDateMS  string `json:"grace_period_expires_date_ms,omitempty"`
		GracePeriodDatePST string `json:"grace_period_expires_date_pst,omitempty"`
	}

	// AutoRenewStatusChangeDate type indicates the auto renew status change date
	AutoRenewStatusChangeDate struct {
		AutoRenewStatusChangeDate    string `json:"auto_renew_status_change_date"`
		AutoRenewStatusChangeDateMS  string `json:"auto_renew_status_change_date_ms"`
		AutoRenewStatusChangeDatePST string `json:"auto_renew_status_change_date_pst"`
	}

	// The InApp type has the receipt attributes
	InApp struct {
		Quantity                    string `json:"quantity"`
		ProductID                   string `json:"product_id"`
		TransactionID               string `json:"transaction_id"`
		OriginalTransactionID       string `json:"original_transaction_id"`
		WebOrderLineItemID          string `json:"web_order_line_item_id,omitempty"`
		PromotionalOfferID          string `json:"promotional_offer_id"`
		SubscriptionGroupIdentifier string `json:"subscription_group_identifier"`
		OfferCodeRefName            string `json:"offer_code_ref_name,omitempty"`

		IsTrialPeriod        string `json:"is_trial_period"`
		IsInIntroOfferPeriod string `json:"is_in_intro_offer_period,omitempty"`
		IsUpgraded           string `json:"is_upgraded,omitempty"`

		ExpiresDate
		PurchaseDate
		OriginalPurchaseDate
		CancellationDate

		CancellationReason string `json:"cancellation_reason,omitempty"`
		InAppOwnershipType string `json:"in_app_ownership_type,omitempty"`
	}

	// The Receipt type has whole data of receipt
	Receipt struct {
		ReceiptType                string  `json:"receipt_type"`
		AdamID                     int64   `json:"adam_id"`
		AppItemID                  int64   `json:"app_item_id"`
		BundleID                   string  `json:"bundle_id"`
		ApplicationVersion         string  `json:"application_version"`
		DownloadID                 int64   `json:"download_id"`
		VersionExternalIdentifier  int     `json:"version_external_identifier"`
		OriginalApplicationVersion string  `json:"original_application_version"`
		InApp                      []InApp `json:"in_app"`
		ReceiptCreationDate
		ReceiptExpirationDate
		RequestDate
		OriginalPurchaseDate
		PreorderDate
	}

	// PendingRenewalInfo is struct
	// A pending renewal may refer to a renewal that is scheduled in the future or a renewal that failed in the past for some reason.
	// https://developer.apple.com/documentation/appstoreservernotifications/unified_receipt/pending_renewal_info
	PendingRenewalInfo struct {
		SubscriptionExpirationIntent   string `json:"expiration_intent"`
		SubscriptionAutoRenewProductID string `json:"auto_renew_product_id"`
		SubscriptionRetryFlag          string `json:"is_in_billing_retry_period"`
		SubscriptionAutoRenewStatus    string `json:"auto_renew_status"`
		SubscriptionPriceConsentStatus string `json:"price_consent_status"`
		ProductID                      string `json:"product_id"`
		OriginalTransactionID          string `json:"original_transaction_id"`
		OfferCodeRefName               string `json:"offer_code_ref_name,omitempty"`
		GracePeriodDate
	}

	// The IAPResponse type has the response properties
	// We defined each field by the current IAP response, but some fields are not mentioned
	// in the following Apple's document;
	// https://developer.apple.com/library/ios/releasenotes/General/ValidateAppStoreReceipt/Chapters/ReceiptFields.html
	// If you get other types or fields from the IAP response, you should use the struct you defined.
	IAPResponse struct {
		Status             int                  `json:"status"`
		Environment        string               `json:"environment"`
		Receipt            Receipt              `json:"receipt"`
		LatestReceiptInfo  []InApp              `json:"latest_receipt_info,omitempty"`
		LatestReceipt      string               `json:"latest_receipt,omitempty"`
		PendingRenewalInfo []PendingRenewalInfo `json:"pending_renewal_info,omitempty"`
		IsRetryable        bool                 `json:"is-retryable,omitempty"`
	}

	StatusResponse struct {
		Status int `json:"status"`
	}

	SubscriptionStatusResponse struct {
		AppAppleID  int                                `json:"appAppleId,omitempty"`
		BundleID    string                             `json:"bundleId"`
		Environment string                             `json:"environment"`
		Data        []*SubscriptionGroupIdentifierItem `json:"data"`
	}

	SubscriptionGroupIdentifierItem struct {
		SubscriptionGroupIdentifier string                  `json:"subscriptionGroupIdentifier"`
		LastTransactions            []*LastTransactionsItem `json:"lastTransactions"`
	}

	LastTransactionsItem struct {
		OriginalTransactionId string `json:"originalTransactionId"`
		SignedRenewalInfo     string `json:"signedRenewalInfo"`
		SignedTransactionInfo string `json:"signedTransactionInfo"`
		Status                int    `json:"status"`
	}
)

type Client struct {
	ProductionURL string
	SandboxURL    string
	HttpCli       *http.Client
}

func (c *Client) DoVerify(ctx context.Context, reqBody IAPRequest, result interface{}) error {
	b := new(bytes.Buffer)
	if err := json.NewEncoder(b).Encode(reqBody); err != nil {
		return err
	}

	req, err := http.NewRequest("POST", c.ProductionURL, b)
	if err != nil {
		return err
	}

	req.Header.Set("Content-Type", IAPRequestContentType)
	req = req.WithContext(ctx)
	resp, err := c.HttpCli.Do(req)
	if err != nil {
		return err
	}

	defer resp.Body.Close()
	if resp.StatusCode >= 500 {
		return fmt.Errorf("Received http status code %d from the App Store: %w", resp.StatusCode, errors.New("AppStore server error"))
	}

	return c.parseResponse(resp, result, ctx, reqBody)
}

func (c *Client) parseResponse(resp *http.Response, result interface{}, ctx context.Context, reqBody IAPRequest) error {
	buf, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	err = json.Unmarshal(buf, &result)
	if err != nil {
		return err
	}

	var r StatusResponse
	err = json.Unmarshal(buf, &r)
	if err != nil {
		return err
	}

	if r.Status == 21007 {
		b := new(bytes.Buffer)
		if err := json.NewEncoder(b).Encode(reqBody); err != nil {
			return err
		}

		req, err := http.NewRequest("POST", c.SandboxURL, b)
		if err != nil {
			return err
		}

		req.Header.Set("Content-Type", IAPRequestContentType)
		req = req.WithContext(ctx)
		resp, err := c.HttpCli.Do(req)
		if err != nil {
			return err
		}

		defer resp.Body.Close()
		if resp.StatusCode >= 500 {
			return fmt.Errorf("Received http status code %d from the App Store Sandbox: %w", resp.StatusCode, errors.New("AppStore server error"))
		}

		return json.NewDecoder(resp.Body).Decode(result)
	}

	return nil
}
