package xphone

import (
	"regexp"
	"strings"

	"github.com/nyaruka/phonenumbers"
	"github.com/spf13/cast"
)

var (
	cnPhoneNumberReg, _ = regexp.Compile(`^(\+86)(1)[0-9]{10}$`)
)

func HideSensitive(phone string) string {
	length := len(phone)
	if length < 4 {
		return phone
	}
	if length <= 8 {
		return "****" + phone[length-4:]
	}
	return phone[:length-8] + "****" + phone[length-4:]
}

func GetLastFour(phone string) string {
	length := len(phone)
	if length < 4 {
		return phone
	}
	return phone[length-4:]
}

func FormatPhoneNumber(phone string) string {
	phone = strings.ReplaceAll(phone, " ", "")
	phone = strings.ReplaceAll(phone, "-", "")
	phone = strings.ReplaceAll(phone, "_", "")
	parse, err := phonenumbers.Parse(phone, "CN")
	if err != nil {
		return phone
	}
	return "+" + cast.ToString(parse.GetCountryCode()) + cast.ToString(parse.GetNationalNumber())
}

func IsCNPhoneNumber(phone string) bool {

	phone = FormatPhoneNumber(phone)

	parse, err := phonenumbers.Parse(phone, "CN")
	if err != nil {
		return false
	}
	if *parse.CountryCode != 86 {
		return false
	}
	return cnPhoneNumberReg.MatchString(phone)
}
