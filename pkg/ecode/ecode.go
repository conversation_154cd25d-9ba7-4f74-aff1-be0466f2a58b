package ecode

import (
	"fmt"
)

var (
	ForbiddenError            = Forbidden(403, "Forbidden")
	NotFound                  = BadRequest(404, "Not Found")
	TooManyRequest            = BadRequest(429, "Too many requests. Please try again later.")
	InternalErr               = InternalServer(500, "Internal Server Error")
	InvalidParams             = NewInvalidParamsErr("Invalid Params")
	InvalidHashID             = BadRequest(601, "invalid hash id")
	TokenTooLong              = BadRequest(603, "Too many words in the query. Please shorten it.")
	InvalidEmbeddingID        = BadRequest(604, "invalid embedding id")
	InvalidConversationID     = BadRequest(605, "invalid conversation id")
	SystemTooLong             = BadRequest(606, "Too many words in the system. Please shorten it.")
	RequestCanceled           = BadRequest(607, "request canceled")
	FeatureNeedLoginUse       = BadRequest(608, "Please login to use this feature.")
	FeatureNeedPremiumUse     = BadRequest(609, "Please upgrade to a premium to use this feature.")
	PeakIntercept             = BadRequest(610, "Sider.ai faces high traffic. Please consider upgrading to a premium account for stable access and to support our development.")
	TwitterGiftDeposited      = BadRequest(611, "Your twitter gift has been deposited.")
	ShouldBreakLLMStream      = BadRequest(612, "ShouldBreakLLMStream")
	InvalidLanguageCode       = BadRequest(613, "Invalid language code")
	NotSupportCurrentLanguage = BadRequest(614, "The current language is not supported")
	InvalidModel              = BadRequest(615, "Invalid AI model")
	NotSupportRegion          = BadRequest(616, "Currently not supported in this region.")

	InvalidIndex = BadRequest(617, "Invalid index")

	// user
	InvalidToken           = BadRequest(1001, "Invalid Token")
	ExpiresToken           = BadRequest(1002, "Token Expires")
	RegisterTooMany        = BadRequest(1003, "Please login to use it")
	InvalidOAuthState      = BadRequest(1004, "Invalid OAuth State")
	IDTokenAlreadyUse      = BadRequest(1005, "IDToken already use")
	DeviceIDHasBind        = BadRequest(1006, "Your device has been bind to an account")
	DeleteAccountFailed    = BadRequest(1007, "Delete Account failed")
	RecentlyDeletedAccount = BadRequest(1008, "Recently deleted the account")
	AvatarFileTooLarge     = BadRequest(1009, "The avatar file is too large.")
	AvatarFileTypeErr      = BadRequest(1010, "The avatar file should be of type image.")
	LoginExpired           = BadRequest(1011, "Your login has expired, please login again")
	LoginKickedByAdmin     = BadRequest(1012, "You have been kicked out, please login again")
	LoginKickedByLimit     = BadRequest(1013, "You've been logged out due to reaching the device limit. You can log in again and this will log out the earliest active device on your account.")
	AlreadyClaimedAward    = BadRequest(1014, "Already claimed the award")
	CurrentUserHasEmail    = BadRequest(1015, "The current user already has a email")
	EmailAccountExisted    = BadRequest(1016, "This email account already exists")
	OAuthAccountExisted    = BadRequest(1017, "This oauth account already exists")
	AlreadyLogout          = BadRequest(1018, "You are logged out. Please log in again")
	CurrentUserIsBlocked   = BadRequest(1019, "Your account has been suspended due to detected unusual activity (e.g. rule violations or account sharing). If you have any questions, please contact us.")
	PleaseUpdateToUsing    = BadRequest(1020, "请更新以继续使用\n\n您好，请允许我们向您道歉，由于我们工作的失误，您下载的该版本Sider存在重大问题，使用该版本会导致跟大模型聊天、付费、多端付费状态同步等功能受到限制。\n\n为了帮助您解决该问题，我们已经紧急修复了问题并提供了最新版本，并为您提供 20 个高级积分的补偿，更新版本后即可获取。(赠送的高级积分可用于任意高级模型:o1&o1-mini,GPT-4o,Claude 3.5Sonnet, Gemini 1.5 Pro,Llama 3.1 405B)\n\n您可以通过以下任意一种方式升级到最新版本:\n· 在我的->关于 Sider 中点击\"版本更新\"，即可更新到最新版本Sider。\n· 在 https://sider.ai/download 下载最新的Sider并直接安装。\n\n注:本次更新较为特殊，您在当前版本的聊天和使用数据将会丢失，如果有需要请自行保存。\n\n如果有任何问题，您可以通过以下方式联系我们:\n邮箱:[<EMAIL>](mailto:<EMAIL>)\nDiscord: https://discord.gg/cD2RXj589d")

	// completion
	CompletionInProgress      = BadRequest(1101, "Your account has an active request - only one request at a time. Please try again later.")
	CompletionUseUp           = BadRequest(1102, "You have used all of the free credits provided by us, please follow the instructions here to get an API from OpenAI.")
	NoOpenAIApiKey            = BadRequest(1103, "No Api Key")
	GetOpenAIApiKeyTimeout    = BadRequest(1104, "Wait ApiKey Timeout")
	ChatUseUp                 = BadRequest(1107, "You have used all of the free credits provided by us. ChatGPT API is expensive, so please upgrade to our premium plan to support us. You can also use your own API key for free if you have.")
	NoOpenAIGPT4ApiKey        = BadRequest(1108, "No GPT4 Api Key")
	GPT4RateLimit             = BadRequest(1109, "GPT-4 limit reach due to high load, please try again later")
	OpenaiRateLimit           = BadRequest(1110, "GPT limit reach due to high load, please try again later")
	ServerExceededGPT4Quota   = BadRequest(1111, "Sider is too popular now. We are waiting for OpenAI to increase the gpt4 usage limit. Please stay tuned and we will be back soon.")
	GPT4UseUp                 = BadRequest(1112, "Invite your friends or upgrade to premium to get more advanced text queries.")
	GPT4UseUpForPremium       = BadRequest(1113, "Invite your friends to get more advanced text queries.")
	GPT4UserRateLimit         = BadRequest(1114, "You have reached the present usage restriction for GPT-4. You can resume using the default model right away.")
	SensitiveFilter           = BadRequest(1115, "内容可能涉及所在国家的敏感信息，无法回答。")
	SearchFailed              = BadRequest(1116, "Failed to query real-time content.")
	SearchUseUp               = BadRequest(1117, "You have used all of the search count provided by us.")
	ImageQuotaErr             = BadRequest(1118, "Your quota of image is insufficient.")
	ParsingVideoDataFailed    = BadRequest(1119, "Parsing video data failed")
	InvalidVideoSubtitles     = BadRequest(1120, "Invalid video subtitles")
	VideoSubtitlesTooLong     = BadRequest(1121, "The subtitles of the video are too long")
	ClaudeRateLimit           = BadRequest(1122, "claude limit reach due to high load, please try again later")
	ClaudeInstantUseUp        = BadRequest(1123, "You have used all of the free credits provided by us. Claude API is expensive, so please upgrade to our premium plan to support us.")
	Claude2UseUp              = BadRequest(1124, "You have used all of the free credits provided by us. Claude API is expensive, so please upgrade to our premium plan to support us.")
	GoogleRateLimit           = BadRequest(1125, "Google limit reach due to high load, please try again later")
	GoogleSecurity            = BadRequest(1126, "Due to google security policy, we are unable to process this request")
	SummarizeUseUp            = BadRequest(1128, "Free usage limit exceeded. Please upgrade to a premium member to use this feature.")
	OCRUseUp                  = BadRequest(1127, "You have used all of the free credits provided by us. ChatGPT API is expensive, so please upgrade to our premium plan to support us. ")
	BasicRateLimit            = BadRequest(1129, "You have reached the present usage restriction for basic text. You can resume using the other model right away.")
	AdvancedRateLimit         = BadRequest(1130, "You have reached the present usage restriction for advanced text. You can resume using the other model right away.")
	ImageChatRateLimit        = BadRequest(1131, "image chat limit reach due to high load, please try again later")
	OpenAIContentFilter       = BadRequest(1132, "The response was filtered due to the prompt triggering OpenAI’s content management policy. Please modify your prompt and retry.")
	PhoneAlreadyBound         = BadRequest(1133, "You have already bound one phone number")
	QAUseUp                   = BadRequest(1134, "You have used up all your daily credits.")
	RateLimitExceeded         = BadRequest(1135, "You've reached the current usage limit. This limit ensures fair use for all users. Please try again later.")
	GPTO1RateLimit            = BadRequest(1136, "The o1 model is experiencing high demand and has reached its quota limit. Use another model or try again later.")
	UnlimitedAdvancedExceeded = BadRequest(1137, "You've hit a temporary limit on Advanced Credits usage. Your current limit is 5 uses every 3 hours, resetting at [timestamp]. Full access returns on your next quota reset. Need more now? Try a booster pack!")
	UnlimitedFirstExceeded    = BadRequest(1138, "Thanks for using unlimited plans! You can still continue to use advanced AI models. However, You’ve reached a temporary speed limit. Here is why there is a speed limit and how we are trying to help you. Please READ IT.\n\nTo not interrupt your current work, We’ve given you some extra advanced queries as a gift to unlock the current speed limit. After you use all these extra advanced queries, you will have a speed limit.")
	UnlimitedNormalExceeded   = BadRequest(1139,
		"Thanks for using unlimited plans! You can still continue to use advanced AI models. However, You’ve reached a temporary speed limit and use up all the extra advanced queries. Here is why there is a speed limit and how we are trying to help you. Please READ IT.\n\nCurrent speed limit is (3 times / 5 hours) on Advanced Queries, resetting at [timestamp]. We may adjust it according to our capacities. Need more now? Try a booster pack!")
	UnlimitedExceededAwardExtra = BadRequest(1140,
		"Thanks for using unlimited plans! You can still continue to use advanced AI models. However, You’ve reached a temporary speed limit. Here is why there is a speed limit and how we are trying to help you. Please READ IT. \n\nWe’ve given you extra 50 advanced queries as a gift to unlock the current speed limit. After you use all these extra advanced credits, you will have a speed limit.")
	UnlimitedExceededAwardExtraUseUp = BadRequest(1141, "Thanks for using unlimited plans! You can still continue to use advanced AI models. However, You’ve reached a temporary speed limit and use up all the gift advanced queries. Here is why there is a speed limit and how we are trying to help you. Please READ IT.\n\nCurrent speed limit is (3 times / 5 hours) on Advanced Queries, resetting at [timestamp]. We may adjust it according to our capacities. Need more now? Try a booster pack!")
	DataAnalysisNeedCredit           = BadRequest(1142, "This answer needs Data Analysis (1 Advanced Credit). You're out of Advanced Credits, so upgrade or invite friends for more!")
	TranslateUseUp                   = BadRequest(1143, "Your Basic Credit for this month is used up. Please wait for the reset next month (subscription date), or upgrade for more.")
	GPT4Dot5RateLimit                = BadRequest(1144, "You've reached your daily limit for GPT-4.5. Due to OpenAI quota restrictions, usage is limited to 1 time per user per day. Please try again 24 hours later or switch to another available model to continue.")

	// payment
	CreateSubscriptionFailed     = BadRequest(1201, "Create subscription failed")
	PaymentFailed                = BadRequest(1202, "Payment failed")
	NoPermission                 = BadRequest(1203, "No permission")
	AlreadyPaid                  = BadRequest(1204, "Already paid")
	InvalidPaymentSession        = BadRequest(1205, "Invalid payment session")
	InvalidReceiptData           = BadRequest(1206, "Invalid receipt data")
	InternalDataAccessError      = BadRequest(1207, "Internal data access errors")
	InvalidPaymentStatus         = BadRequest(1208, "Invalid payment status")
	InvalidReceipt               = BadRequest(1209, "Invalid receipt")
	RecoverFailed                = BadRequest(1210, "Recover failed")
	DuplicatePayments            = BadRequest(1211, "Duplicate payments")
	VerifyFailed                 = BadRequest(1212, "Verify failed")
	InvalidProduct               = BadRequest(1213, "Invalid product")
	InvalidDevice                = BadRequest(1214, "Invalid device")
	NeedLoginAccount             = BadRequest(1215, "Please log into your paid account")
	UserBadRequest               = BadRequest(1216, "Bad request")
	InvalidConversationMessageID = BadRequest(1217, "invalid conversation message id")
	InvalidPromptTemplateKey     = BadRequest(1218, "invalid prompt template key")
	InvalidToolName              = BadRequest(1219, "invalid tool name")
	ProductIDNotMatch            = BadRequest(1220, "Product ID doesn't match")
	PaymentTimeout               = BadRequest(1221, "Payment timeout")
	PaymentTrialFailed           = BadRequest(1222, "Only one free trial is allowed per customer")
	PaymentTrialInvalidCard      = BadRequest(1223, "This card was previously used for a Free Trial of Sider")
	ChatModelsLimit              = BadRequest(1224, "Too many models mentioned. Maximum is 8 per chat.")
	AcknowledgeFailed            = BadRequest(1225, "Acknowledge failed")
	PromotionFinished            = BadRequest(1234, "The promotion is finished")
	NeedLoginPaidAccount         = BadRequest(1235, "You have already paid with another account, please log into your paid account")
	NoSubscriptionData           = BadRequest(1236, "No subscription data")
	NoTransactionData            = BadRequest(1237, "No transaction")
	InvalidAuthKey               = BadRequest(1238, "Invalid auth key")
	PaymentInProgress            = BadRequest(1239, "Payment may still be in progress")
	InvalidGooglePayToken        = BadRequest(1240, "Invalid google pay token")

	// db
	UpdateFailed = BadRequest(1301, "Update failed")
	CreateFailed = BadRequest(1302, "Create failed")

	// conversation
	ShareKeyInvalid      = BadRequest(1401, "invalid share")
	PrivateChat          = BadRequest(1402, "private chat")
	ConversationNotMatch = BadRequest(1403, "The current conversation does not belong to the current account")

	// completion & openai
	OpenAIServerBusy         = BadRequest(1501, "Openai server is busy. Please switch to Claude during the OpenAl outage.")
	OpenAITimeout            = BadRequest(1502, "openai response timeout")
	ClientStopCompletion     = BadRequest(1503, "client stop completion")
	OpenAIExceededQuota      = BadRequest(1504, "Sider is too popular now. We are waiting for OpenAI to increase the usage limit.  Please stay tuned and we will be back soon.")
	OpenAITimeoutWithClient  = BadRequest(1505, "openai response timeout")
	OpenAIStopResponse       = BadRequest(1506, "openai stop response")
	OpenAIResponseFmtErr     = BadRequest(1507, "openai response format error")
	LangchainNoFinalAnswer   = BadRequest(1508, "langchain no final answer")
	ClaudeStopResponse       = BadRequest(1509, "claude stop response")
	ClaudeTimeoutWithClient  = BadRequest(1510, "claude response timeout")
	ClaudeTimeout            = BadRequest(1511, "claude response timeout")
	ClaudeServerBusy         = BadRequest(1512, "claude server is busy")
	GoogleStopResponse       = BadRequest(1513, "google stop response")
	GoogleTimeout            = BadRequest(1514, "google response timeout")
	GoogleServerBusy         = BadRequest(1515, "google server is busy")
	UnexpectedFunctionCall   = BadRequest(1516, "get unexpected function call")
	GoogleStopResponseSafety = BadRequest(1517, "Generation was stopped due to the response being flagged as unsafe.")
	GoogleQuotaExceeded      = BadRequest(1518, "Quota exceeded.")
	LLMResponseTimeout       = BadRequest(1519, "LLM response timeout")
	LLMServerBusy            = BadRequest(1520, "The upstream service is busy. Please try again later.")

	// sms
	InvalidSmsImageCaptcha             = BadRequest(1601, "Invalid Image Captcha")
	InvalidSmsCaptcha                  = BadRequest(1602, "Invalid Captcha")
	MaxCheckAttemptsReached            = BadRequest(1603, "Max check attempts reached")
	MaxSendAttemptsReached             = BadRequest(1604, "Max send attempts reached")
	InvalidPhoneNumber                 = BadRequest(1605, "Invalid Phone Number")
	VerificationDeliveryAttemptBlocked = BadRequest(1606, "Verification delivery attempt blocked")
	NotSupportPhoneNumberLoginRegion   = BadRequest(1610, "This region does not support login via phone number.\nPlease use an alternative login method.")

	// invite
	InvalidReferralKey     = BadRequest(1701, "Invalid referral key")
	AcceptInvitationFailed = BadRequest(1702, "Failed to accept invitation")
	AlreadyAccepted        = BadRequest(1703, "You have already accepted the invitation")
	AlreadyClaimedReward   = BadRequest(1704, "You have already claimed the reward")
	FailedToClaimReward    = BadRequest(1705, "Failed to claim reward")
	ExceedingTheLimit      = BadRequest(1706, "Exceeding the limit")

	// robot
	UploadFailed = BadRequest(1720, "Upload failed")

	// image generate err 1800
	StabilityContentModeration = BadRequest(1801, "Your request was flagged by content moderation system, as a result your request was denied and you were not charged.")
	// image size exceed error 1802
	ImagePromptLengthExceed = BadRequest(1803, "The image generation prompt you entered is too long, please simplify the description.")

	// embedding
	EmbeddingNotFinish       = BadRequest(2001, "embedding not finish")
	EmbeddingFailed          = BadRequest(2002, "embedding failed")
	EmbeddingLimitByCount    = BadRequest(2003, "embedding limit by count")
	EmbeddingLimitByPages    = BadRequest(2004, "embedding limit by pages")
	EmbeddingLimitBySize     = BadRequest(2005, "embedding limit by size")
	EmbeddingFolderNameExist = BadRequest(2006, "folder name already exist")

	// upload pdf by url.
	UnableToDownloadPDF          = BadRequest(2007, "unable to download PDF from URL")
	FailedToDownloadPDF          = BadRequest(2008, "failed to download PDF, HTTP status")
	ContentTypeNotPDF            = BadRequest(2009, "URL response Content-Type is not PDF")
	FailedToCreateTempFile       = BadRequest(2010, "Failed to create temporary file")
	FailedToSavePDFFile          = BadRequest(2011, "Failed to save PDF file")
	FailedToCreateFormFilePart   = BadRequest(2012, "Failed to create form file part")
	FailedToResetFilePointer     = BadRequest(2013, "Failed to reset file pointer")
	FailedToWritePDFDataToForm   = BadRequest(2014, "Failed to write PDF data to form")
	FailedToWriteTimeZoneField   = BadRequest(2015, "Failed to write TimeZone field")
	FailedToWriteAppNameField    = BadRequest(2016, "Failed to write app_name field")
	FailedToWriteAppVersionField = BadRequest(2017, "Failed to write app_version field")
	FailedToCloseMultipartWriter = BadRequest(2018, "Failed to close multipart writer")
	FailedToCreateNewRequest     = BadRequest(2019, "Failed to create new request")
	FailedToParseMultipartForm   = BadRequest(2020, "Failed to parse multipart form data")
	FileNotFoundInForm           = BadRequest(2021, "File not found in form")
	// grammar
	TryAgainLater      = BadRequest(2401, "Try again later")
	GrammarCheckFailed = BadRequest(2402, "Grammar check failed")

	// file
	FileNotBelongUser       = BadRequest(3000, "The file does not belong to the current user")
	FileUnsupportedType     = BadRequest(3001, "Invalid file type")
	NoSubtitles             = BadRequest(3101, "This video has no subtitles")
	GetVideoSubtitlesFailed = BadRequest(3102, "Failed to get video subtitles")
	CreateVideoClipsFailed  = BadRequest(3103, "Create video clips failed")
	GetVideoInfoFailed      = BadRequest(3104, "Get video info failed")
	VideoCreateRetryFailed  = BadRequest(3105, "Retry failed")
	InvalidVideoStatus      = BadRequest(3106, "Invalid video status")

	// audio
	VoiceNotSupport                   = BadRequest(3500, "The voice is not supported")
	TTSQuotaUseUp                     = BadRequest(3501, "Your TTS quota has been used up")
	TTSTooManyWsConn                  = BadRequest(3502, "Too many websocket connections")
	TTSWsReadTimeout                  = BadRequest(3503, "Websocket read time out")
	InvalidTTSKey                     = BadRequest(3504, "invalid tts key")
	WordBoundariesNotExist            = BadRequest(3505, "word boundaries not exits")
	TTSTooManyChar                    = BadRequest(3506, "AI Read Aloud has a 1000-character limit per selection. Please select a shorter text and try again.")
	AzureTTSEmptyContent              = BadRequest(3507, "Azure TTS server is busy, please try again later")
	SiderAudioCaptionExtractionFailed = BadRequest(3508, "Extraction failed")
	NotAdvancedCreditToUseAudio       = BadRequest(3509, "You need more Advanced Credits. Each 10 minutes (or less) of audio costs 1 Advanced Credit. Upgrade or invite friends to get more!")
	InvalidAudioFile                  = BadRequest(3510, "Invalid audio file")
	AudioServerOverloaded             = BadRequest(3511, "Audio-to-text service is currently overloaded, please try again later")

	MaximumVideoDuration = BadRequest(3601, "Maximum video duration reached. There may be a queue due to high demand. For prioritized access, <NAME_EMAIL>")

	// credits
	FeatureUseUp = BadRequest(4000, "You’ve reached the trial limit for the premium feature. Upgrade to unlock all features.")

	// whisper
	VideoTooLong                = BadRequest(4500, "video too long")
	FailedToCreateUserVideoTask = BadRequest(4501, "failed to create user video task")
	FailedToSendWhisperRequest  = BadRequest(4502, "failed to send whisper request")
	GotErrorResponseFromWhisper = BadRequest(4503, "got error response from whisper")
	FailedToGetSubtitle         = BadRequest(4504, "failed to get subtitle")
	FailedToGetVideoInfo        = BadRequest(4505, "failed to get video info")
	VideoIsInLive               = BadRequest(4506, "Live video summarization is not supported at this time")

	// chat share
	UserAuthenticationFailed   = BadRequest(5001, "User authentication failed")
	FailedToGetShare           = BadRequest(5002, "Failed to get share")
	FailedToRevoke             = BadRequest(5003, "Failed to revoke")
	FailedToCreateShare        = BadRequest(5004, "Failed to create share")
	InvalidJSONContent         = BadRequest(5005, "Invalid JSON content")
	NoPermissionToRevoke       = BadRequest(5006, "share not found, already revoked, or you don't have permission to revoke it")
	ErrShareNotFoundOrNotOwned = BadRequest(5007, "Share not found or not owned by you")
	NotYourImage               = BadRequest(5008, "Not your Image")
	FailedToGetTitleFromLLM    = BadRequest(5009, "Failed to get title from LLM")

	// artifact_publish
	FailedtoPublishArtifact  = BadRequest(5101, "Failed to publish artifact")
	ArtifactNotFound         = BadRequest(5102, "Artifact not found")
	FailedtoRetrieveArtifact = BadRequest(5103, "Failed to retrieve artifact")
	NotYourArtifact          = BadRequest(5104, "Not your Artifact")
	FailedtoMarshalAttrs     = BadRequest(5105, "Failed to marshal attrs")
	FailedtoUnmarshalAttrs   = BadRequest(5106, "Failed to unmarshal attrs")
	FailedtoGenerateUUID     = BadRequest(5107, "Failed to generate uuid")

	UpgradeExtension = BadRequest(5200, "Upgrade to the latest extension version before summarizing")

	// ask
	QuestionTooLong = BadRequest(5501, "Question too long")

	// Vela
	VelaRedeemInvalidCode = BadRequest(5601, "Invalid Code")
	VelaRedeemCannotUse   = Forbidden(5602, "Cannot Redeem")

	ModelPlatformIsBusy = BadRequest(10002, "Model platform is busy")
)

func init() {
	checkCodeRegister()
}

func NewInvalidParamsErr(msg string) *Error {
	return BadRequest(1000, msg)
}

func NewStabilityAiErr(msg string) *Error {
	return BadRequest(1800, msg)
}

func NewIdeoGramErr(msg string) *Error {
	return BadRequest(1800, "IdeoGram Request Failed: "+msg)
}

func NewImageSizeExceededErr(attribute string, get, need int64) *Error {
	msg := fmt.Sprintf("Image too large, %s exceeded: %d > %d", attribute, get, need)
	return BadRequest(1802, msg)
}

func IsKnownErr(err error) bool {
	if eErr := FromError(err); eErr.Code != UnknownCode {
		return true
	}
	return false
}

func ReplaceErrIfUnKnown(err, replace error) error {
	if IsKnownErr(err) {
		return err
	}
	return replace
}
