//go:build wireinject
// +build wireinject

package main

import (
	"context"

	"github.com/google/wire"

	"imagezero/configs"
	"imagezero/internal/clients"
	"imagezero/internal/cmd"
	"imagezero/internal/crontab"
	"imagezero/internal/data"
	"imagezero/internal/routes"
	"imagezero/internal/service"
)

func app(ctx context.Context) (*cmd.App, func(), error) {
	panic(wire.Build(
		configs.InitConfig,
		clients.ProviderSet,
		routes.ProviderSet,
		data.ProviderSet,
		service.ProviderSet,
		crontab.ProviderSet,
		cmd.ProviderSet,
	))
}
